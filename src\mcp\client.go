package mcp

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// MCPClient MCP客户端
type MCPClient struct {
	BaseURL string
	Client  *http.Client
}

// SearchRequest 搜索请求结构
type SearchRequest struct {
	Query   string   `json:"query"`
	Limit   int      `json:"limit,omitempty"`
	Engines []string `json:"engines,omitempty"`
}

// SearchResult 搜索结果结构
type SearchResult struct {
	Title       string `json:"title"`
	URL         string `json:"url"`
	Description string `json:"description"`
	Source      string `json:"source"`
	Engine      string `json:"engine"`
}

// FetchArticleRequest 获取文章请求结构
type FetchArticleRequest struct {
	URL string `json:"url"`
}

// FetchArticleResult 获取文章结果结构
type FetchArticleResult struct {
	Content string `json:"content"`
}

// MCPToolRequest MCP工具请求结构
type MCPToolRequest struct {
	Method string    `json:"method"`
	Params MCPParams `json:"params"`
}

// MCPParams MCP参数结构
type MCPParams struct {
	Name      string      `json:"name"`
	Arguments interface{} `json:"arguments"`
}

// MCPResponse MCP响应结构
type MCPResponse struct {
	Result interface{} `json:"result,omitempty"`
	Error  *MCPError   `json:"error,omitempty"`
}

// MCPError MCP错误结构
type MCPError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// NewMCPClient 创建新的MCP客户端
func NewMCPClient(baseURL string) *MCPClient {
	return &MCPClient{
		BaseURL: baseURL,
		Client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// Search 执行网络搜索
func (c *MCPClient) Search(query string, limit int, engines []string) ([]SearchResult, error) {
	if limit <= 0 {
		limit = 5
	}
	if engines == nil {
		engines = []string{"bing", "baidu"}
	}

	request := MCPToolRequest{
		Method: "tools/call",
		Params: MCPParams{
			Name: "search",
			Arguments: SearchRequest{
				Query:   query,
				Limit:   limit,
				Engines: engines,
			},
		},
	}

	response, err := c.callTool(request)
	if err != nil {
		return nil, err
	}

	// 解析搜索结果
	var results []SearchResult
	if response.Result != nil {
		resultBytes, err := json.Marshal(response.Result)
		if err != nil {
			return nil, fmt.Errorf("解析搜索结果失败: %v", err)
		}

		if err := json.Unmarshal(resultBytes, &results); err != nil {
			return nil, fmt.Errorf("反序列化搜索结果失败: %v", err)
		}
	}

	return results, nil
}

// FetchCSDNArticle 获取CSDN文章内容
func (c *MCPClient) FetchCSDNArticle(url string) (*FetchArticleResult, error) {
	request := MCPToolRequest{
		Method: "tools/call",
		Params: MCPParams{
			Name: "fetchCsdnArticle",
			Arguments: FetchArticleRequest{
				URL: url,
			},
		},
	}

	response, err := c.callTool(request)
	if err != nil {
		return nil, err
	}

	// 解析文章内容
	var result FetchArticleResult
	if response.Result != nil {
		resultBytes, err := json.Marshal(response.Result)
		if err != nil {
			return nil, fmt.Errorf("解析文章内容失败: %v", err)
		}

		if err := json.Unmarshal(resultBytes, &result); err != nil {
			return nil, fmt.Errorf("反序列化文章内容失败: %v", err)
		}
	}

	return &result, nil
}

// FetchLinuxDoArticle 获取Linux.do文章内容
func (c *MCPClient) FetchLinuxDoArticle(url string) (*FetchArticleResult, error) {
	request := MCPToolRequest{
		Method: "tools/call",
		Params: MCPParams{
			Name: "fetchLinuxDoArticle",
			Arguments: FetchArticleRequest{
				URL: url,
			},
		},
	}

	response, err := c.callTool(request)
	if err != nil {
		return nil, err
	}

	// 解析文章内容
	var result FetchArticleResult
	if response.Result != nil {
		resultBytes, err := json.Marshal(response.Result)
		if err != nil {
			return nil, fmt.Errorf("解析文章内容失败: %v", err)
		}

		if err := json.Unmarshal(resultBytes, &result); err != nil {
			return nil, fmt.Errorf("反序列化文章内容失败: %v", err)
		}
	}

	return &result, nil
}

// callTool 调用MCP工具
func (c *MCPClient) callTool(request MCPToolRequest) (*MCPResponse, error) {
	jsonData, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %v", err)
	}

	req, err := http.NewRequest("POST", c.BaseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.Client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("MCP请求失败 (状态码: %d): %s", resp.StatusCode, string(body))
	}

	var mcpResponse MCPResponse
	if err := json.Unmarshal(body, &mcpResponse); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v, 原始响应: %s", err, string(body))
	}

	if mcpResponse.Error != nil {
		return nil, fmt.Errorf("MCP错误: %s", mcpResponse.Error.Message)
	}

	return &mcpResponse, nil
}

// IsAvailable 检查MCP服务是否可用
func (c *MCPClient) IsAvailable() bool {
	// 尝试访问健康检查端点
	healthURL := strings.Replace(c.BaseURL, "/mcp", "/health", 1)
	req, err := http.NewRequest("GET", healthURL, nil)
	if err != nil {
		return false
	}

	resp, err := c.Client.Do(req)
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	// 健康检查端点应该返回200
	if resp.StatusCode == http.StatusOK {
		return true
	}

	// 如果健康检查失败，尝试直接访问MCP端点
	// MCP端点可能返回405 Method Not Allowed，这也表示服务可用
	req2, err := http.NewRequest("GET", c.BaseURL, nil)
	if err != nil {
		return false
	}

	resp2, err := c.Client.Do(req2)
	if err != nil {
		return false
	}
	defer resp2.Body.Close()

	// 对于MCP端点，405 Method Not Allowed 也表示服务可用
	return resp2.StatusCode == http.StatusOK || resp2.StatusCode == http.StatusMethodNotAllowed
}
