-- 重置AI助手免责声明状态的SQL脚本
-- 用于测试或管理目的

-- 查看所有免责声明相关配置
SELECT * FROM configs WHERE key LIKE 'ai_disclaimer_shown_%';

-- 重置特定用户的免责声明状态（将用户名替换为实际用户名）
-- DELETE FROM configs WHERE key = 'ai_disclaimer_shown_admin';

-- 重置所有用户的免责声明状态（谨慎使用）
-- DELETE FROM configs WHERE key LIKE 'ai_disclaimer_shown_%';

-- 手动设置用户免责声明状态为已显示
-- INSERT OR REPLACE INTO configs (key, value) VALUES ('ai_disclaimer_shown_admin', 'true');

-- 查看配置表结构
-- PRAGMA table_info(configs);
