package ui

import (
	"fmt"
	"os"
	"path/filepath"
	"simple_inventory_management_system/internal/database"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
)

// createAIChatTab 创建AI对话标签页
func (a *App) createAIChatTab() fyne.CanvasObject {
	// 获取当前工作目录
	currentDir, err := os.Getwd()
	if err != nil {
		dialog.ShowError(fmt.Errorf("无法获取当前目录: %v", err), a.mainWin)
		return widget.NewLabel("加载失败")
	}

	// 构建HTML文件的绝对路径
	htmlPath := filepath.Join(currentDir, "web_ui", "index.html")

	// 检查HTML文件是否存在
	if _, err := os.Stat(htmlPath); os.IsNotExist(err) {
		return container.NewCenter(
			container.NewVBox(
				widget.NewLabel("AI对话功能暂不可用"),
				widget.NewLabel(fmt.Sprintf("找不到文件: %s", htmlPath)),
				widget.NewButton("刷新", func() {
					// 重新创建tab内容
					a.refreshAIChatTab()
				}),
			),
		)
	}

	// 检查是否需要显示免责声明
	a.checkAndShowAIDisclaimer()

	// 创建WebView组件
	webView := NewWebView()

	// 设置窗口引用（用于显示复制通知）
	webView.SetWindow(a.mainWin)

	// 设置HTML文件URL
	fileURL := fmt.Sprintf("file:///%s", filepath.ToSlash(htmlPath))
	webView.LoadURL(fileURL)

	// 创建工具栏
	toolbar := container.NewHBox(
		widget.NewButton("刷新", func() {
			webView.Reload()
		}),
		widget.NewButton("重置", func() {
			// 重新加载页面
			webView.LoadURL(fileURL)
		}),
	)

	// 使用Border布局，工具栏在顶部，WebView在中心
	return container.NewBorder(
		toolbar, // 顶部：工具栏
		nil,     // 底部：无
		nil,     // 左侧：无
		nil,     // 右侧：无
		webView, // 中心：WebView组件
	)
}

// refreshAIChatTab 刷新AI对话标签页
func (a *App) refreshAIChatTab() {
	// 这个方法可以用于刷新标签页内容
	// 在实际应用中，可以重新创建WebView或重新加载内容
}

// checkAndShowAIDisclaimer 检查并显示AI免责声明
func (a *App) checkAndShowAIDisclaimer() {
	// 检查当前用户是否已经看过免责声明
	configKey := fmt.Sprintf("ai_disclaimer_shown_%s", a.user.Username)
	disclaimerShown, err := database.GetConfig(configKey)
	if err != nil || disclaimerShown != "true" {
		// 显示免责声明弹框
		a.showAIDisclaimer(configKey)
	}
}

// showAIDisclaimer 显示AI免责声明弹框
func (a *App) showAIDisclaimer(configKey string) {
	// 创建免责声明内容
	disclaimerText := `AI人工助手内容都是AI生成，结果不一定正确且可能过于老旧，请上网搜索确认其结果的正确性和及时性。

使用AI助手即表示您已了解并同意：
• AI生成的内容仅供参考，不构成专业建议
• 请独立验证AI提供的信息准确性
• 对于重要决策，建议咨询相关专业人士
• 本系统不对AI生成内容的准确性承担责任`

	// 创建内容标签
	contentLabel := widget.NewLabel(disclaimerText)
	contentLabel.Wrapping = fyne.TextWrapWord

	// 创建滚动容器
	scrollContent := container.NewScroll(contentLabel)
	scrollContent.Resize(fyne.NewSize(500, 300))

	// 创建对话框内容
	content := container.NewVBox(
		widget.NewLabel("AI助手免责声明"),
		widget.NewSeparator(),
		scrollContent,
	)

	// 创建自定义对话框
	d := dialog.NewCustom("重要提示", "我已了解", content, a.mainWin)
	d.Resize(fyne.NewSize(550, 400))

	// 设置对话框回调
	d.SetOnClosed(func() {
		// 用户关闭对话框后，标记为已显示
		database.AddConfig(configKey, "true")
	})

	// 显示对话框
	d.Show()
}
