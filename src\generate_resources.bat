@echo off
echo 正在生成资源文件...

REM 检查fyne命令是否存在
fyne version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 未找到fyne命令，正在安装...
    go install fyne.io/fyne/v2/cmd/fyne@latest
    if %ERRORLEVEL% NEQ 0 (
        echo fyne安装失败！
        pause
        exit /b 1
    )
)

REM 生成logo资源
if exist "logo\logo.png" (
    echo 生成logo资源...
    fyne bundle -o ui\logo_resource.go logo\logo.png
    if %ERRORLEVEL% EQU 0 (
        echo logo资源生成成功！
    ) else (
        echo logo资源生成失败！
    )
) else (
    echo 未找到logo.png文件
)

REM 生成图标资源
if exist "logo\logo_16x16.ico" (
    echo 生成图标资源...
    fyne bundle -o ui\icon_resource.go logo\logo_16x16.ico
    if %ERRORLEVEL% EQU 0 (
        echo 图标资源生成成功！
    ) else (
        echo 图标资源生成失败！
    )
) else (
    echo 未找到logo_16x16.ico文件
)

echo.
echo 资源生成完成！
echo 现在可以运行 build.bat 来构建应用程序
echo.
pause
