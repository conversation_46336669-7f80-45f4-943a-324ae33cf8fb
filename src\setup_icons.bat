@echo off
echo 图标设置助手
echo ================

REM 检查logo目录是否存在
if not exist "logo" (
    echo 创建logo目录...
    mkdir logo
)

echo.
echo 当前logo目录内容:
dir logo /b 2>nul

echo.
echo 支持的图标格式:
echo 1. PNG格式 - logo.png (推荐64x64或更大)
echo 2. ICO格式 - logo_16x16.ico, logo_24x24.ico
echo.

REM 检查是否有PNG文件
if exist "logo\logo.png" (
    echo ✓ 找到PNG logo文件: logo\logo.png
) else (
    echo ✗ 未找到PNG logo文件: logo\logo.png
)

REM 检查是否有ICO文件
if exist "logo\logo_16x16.ico" (
    echo ✓ 找到16x16 ICO文件: logo\logo_16x16.ico
) else (
    echo ✗ 未找到16x16 ICO文件: logo\logo_16x16.ico
)

if exist "logo\logo_24x24.ico" (
    echo ✓ 找到24x24 ICO文件: logo\logo_24x24.ico
) else (
    echo ✗ 未找到24x24 ICO文件: logo\logo_24x24.ico
)

echo.
echo 图标加载优先级:
echo 1. ICO文件 (logo_24x24.ico, logo_16x16.ico)
echo 2. PNG文件 (logo.png)
echo.

echo 使用说明:
echo 1. 将您的logo图片重命名为 logo.png 并放在 logo\ 目录下
echo 2. 如果有ICO文件，请命名为 logo_16x16.ico 或 logo_24x24.ico
echo 3. 运行 build.bat 重新构建应用程序
echo 4. ICO文件会优先用作应用程序图标，PNG文件用作界面logo
echo.

echo 提示: 
echo - PNG文件建议尺寸: 64x64, 128x128, 或 256x256 像素
echo - ICO文件建议尺寸: 16x16, 24x24, 32x32 像素
echo - 透明背景效果更佳
echo.

pause
