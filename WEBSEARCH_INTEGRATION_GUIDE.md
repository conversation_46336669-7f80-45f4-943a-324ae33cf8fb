# 🔍 AI助手网络搜索功能集成指南

## 功能概述

成功集成了open-websearch MCP服务，为AI助手添加了强大的联网搜索功能！

### ✨ 核心特性
- **多引擎搜索**: 支持Bing、百度、CSDN等多个搜索引擎
- **智能触发**: 自动检测用户问题是否需要搜索
- **无API密钥**: 完全免费，无需任何API密钥
- **实时信息**: 获取最新、准确的网络信息
- **结果整合**: 将搜索结果智能整合到AI回复中

## 🚀 快速开始

### 1. 启动搜索服务
```bash
# 方法1: 使用提供的启动脚本（推荐）
start_websearch.bat

# 方法2: 手动使用Docker Compose
docker-compose -f docker-compose-websearch.yml up -d

# 方法3: 直接使用Docker
docker run -d -p 3000:3000 -e ENABLE_CORS=true ghcr.io/aas-ee/open-web-search:latest
```

### 2. 启动AI助手
```bash
cd src
websearch_test.exe
```

### 3. 配置搜索功能
1. 登录AI助手系统
2. 点击"AI助手"标签页
3. 点击"搜索设置"按钮
4. 启用"启用网络搜索增强"
5. 确认MCP服务地址：`http://localhost:3000/mcp`
6. 点击"测试连接"验证服务可用性
7. 点击"保存设置"

## 🎯 使用方法

### 自动触发搜索
当用户消息包含以下关键词时，会自动触发网络搜索：

**中文关键词**:
- 搜索、查找、找一下、搜一下、查询
- 最新、新闻、资讯、动态、趋势
- 什么是、如何、怎么、为什么
- 教程、方法、步骤、指南
- 比较、对比、区别、差异
- 推荐、建议、选择、哪个好
- 价格、报价、多少钱、费用
- 评测、评价、口碑、怎么样

**英文关键词**:
- search、find、lookup、what is、how to

**问句检测**:
- 包含问号（？或?）的消息

### 使用示例

**用户**: "最新的AI技术发展趋势是什么？"
**系统**: 自动搜索 → 获取最新信息 → 整合到AI回复中

**用户**: "如何使用Docker部署应用？"
**系统**: 搜索相关教程 → 提供详细步骤和最佳实践

**用户**: "比较一下不同的编程语言"
**系统**: 搜索对比信息 → 提供全面的比较分析

## 🔧 技术架构

### 组件结构
```
AI助手系统
├── MCP客户端 (src/mcp/client.go)
├── 搜索增强器 (src/ai/search_enhancer.go)
├── WebView界面 (src/ui/webview.go)
└── 配置管理 (数据库配置)

外部服务
└── open-websearch MCP服务 (Docker容器)
```

### 数据流程
1. **用户输入** → 消息预处理
2. **关键词检测** → 判断是否需要搜索
3. **MCP调用** → 执行多引擎搜索
4. **结果整合** → 将搜索结果添加到消息上下文
5. **AI处理** → OpenRouter API生成增强回复
6. **用户展示** → 显示包含搜索结果的回复

### 配置项说明
- `enable_web_search`: 是否启用网络搜索 (true/false)
- `mcp_websearch_url`: MCP服务地址 (默认: http://localhost:3000/mcp)

## 🛠️ 管理和维护

### 服务管理命令
```bash
# 查看服务状态
docker-compose -f docker-compose-websearch.yml ps

# 查看服务日志
docker-compose -f docker-compose-websearch.yml logs -f

# 重启服务
docker-compose -f docker-compose-websearch.yml restart

# 停止服务
stop_websearch.bat
# 或
docker-compose -f docker-compose-websearch.yml down
```

### 故障排除

**问题1: 搜索功能不工作**
- 检查MCP服务是否启动：`docker ps | grep open-web-search`
- 测试服务连接：访问 http://localhost:3000/health
- 检查配置：确认"启用网络搜索增强"已勾选

**问题2: 连接测试失败**
- 确认Docker服务正在运行
- 检查端口3000是否被占用：`netstat -an | findstr 3000`
- 重启MCP服务：`docker-compose -f docker-compose-websearch.yml restart`

**问题3: 搜索结果质量不佳**
- 调整搜索引擎组合（在代码中修改engines参数）
- 优化关键词提取逻辑
- 增加搜索结果数量限制

## 🎨 自定义配置

### 修改搜索引擎
在 `src/ai/search_enhancer.go` 中修改：
```go
// 执行搜索时指定引擎
searchResults, err := se.mcpClient.Search(searchQuery, 5, []string{"bing", "baidu", "csdn"})
```

### 调整触发关键词
在 `needsSearch` 方法中添加或修改关键词：
```go
searchTriggers := []string{
    // 添加您的自定义关键词
    "自定义关键词1", "自定义关键词2",
}
```

### 修改搜索结果格式
在 `buildEnhancedMessage` 方法中自定义结果展示格式。

## 📊 性能优化

### 建议配置
- **搜索结果数量**: 3-5个（平衡信息量和响应速度）
- **搜索引擎**: 根据用户群体选择合适的引擎组合
- **缓存策略**: 考虑为常见查询添加缓存机制

### 监控指标
- MCP服务响应时间
- 搜索成功率
- 用户满意度反馈

## 🔒 安全考虑

1. **网络安全**: MCP服务仅监听本地端口
2. **内容过滤**: 搜索结果会经过AI模型处理
3. **隐私保护**: 不存储用户搜索历史
4. **访问控制**: 基于用户权限控制功能使用

## 🚀 未来扩展

### 计划功能
- [ ] 支持更多搜索引擎（Google、Reddit、YouTube）
- [ ] 添加搜索结果缓存机制
- [ ] 实现搜索历史记录
- [ ] 支持图片和视频搜索
- [ ] 添加搜索结果质量评分

### 集成建议
- 考虑集成其他MCP服务（如文档处理、数据分析等）
- 添加用户反馈机制来改进搜索质量
- 实现搜索结果的个性化推荐

---

## 📞 技术支持

如有问题或建议，请：
1. 查看日志文件：`src/logs/app.log`
2. 检查MCP服务状态
3. 参考open-websearch项目文档：https://github.com/Aas-ee/open-webSearch

**恭喜！您的AI助手现在具备了强大的联网搜索能力！** 🎉
