# AI助手免责弹框功能

## 功能概述

为AI助手添加了免责声明弹框功能，确保用户在首次使用AI助手时了解相关风险和限制。

## 功能特点

### 1. 首次访问提醒
- 每个用户首次点击"AI助手"标签页时会自动弹出免责声明
- 基于用户账户进行记录，不同用户独立管理
- 用户确认后不会再次显示（除非清除配置）

### 2. 免责声明内容
弹框包含以下重要信息：
```
AI人工助手内容都是AI生成，结果不一定正确且可能过于老旧，请上网搜索确认其结果的正确性和及时性。

使用AI助手即表示您已了解并同意：
• AI生成的内容仅供参考，不构成专业建议
• 请独立验证AI提供的信息准确性
• 对于重要决策，建议咨询相关专业人士
• 本系统不对AI生成内容的准确性承担责任
```

### 3. 用户体验优化
- 弹框设计美观，包含滚动区域支持长文本显示
- 尺寸适中（550x400像素），不会过于突兀
- 只需点击"我已了解"按钮即可关闭
- 状态持久化保存，重启应用后不会重复显示

## 技术实现

### 1. 数据库配置
- 使用配置表存储用户免责声明确认状态
- 配置键格式：`ai_disclaimer_shown_{username}`
- 配置值：`"true"` 表示已显示并确认

### 2. 代码结构
- `checkAndShowAIDisclaimer()`: 检查是否需要显示免责声明
- `showAIDisclaimer(configKey)`: 显示免责声明弹框
- 集成到 `createAIChatTab()` 函数中，在创建AI助手界面时自动调用

### 3. 文件修改
主要修改文件：`src/ui/ai_chat_tab.go`
- 添加数据库导入
- 新增免责声明检查和显示逻辑
- 集成到AI助手标签页创建流程

## 使用说明

### 用户操作
1. 登录系统后，点击"AI助手"标签页
2. 首次访问时会自动弹出免责声明对话框
3. 阅读免责声明内容
4. 点击"我已了解"按钮确认并关闭对话框
5. 后续访问AI助手不会再显示此弹框

### 管理员操作
如需重置某用户的免责声明状态：
1. 进入"配置管理"标签页
2. 找到对应的配置项：`ai_disclaimer_shown_{username}`
3. 删除该配置项，用户下次访问时会重新显示免责声明

## 测试验证

### 构建测试
```bash
cd src/cmd
go build -o ../test_build.exe .
```

### 功能测试步骤
1. 启动应用程序
2. 使用不同用户账户登录
3. 点击"AI助手"标签页
4. 验证免责声明弹框是否正确显示
5. 确认弹框关闭后不会重复显示
6. 测试不同用户的独立性

## 注意事项

1. **用户隐私**: 免责声明状态基于用户名存储，确保不同用户的独立性
2. **数据持久化**: 配置信息存储在数据库中，应用重启后仍然有效
3. **界面友好**: 弹框设计考虑了用户体验，不会阻碍正常使用
4. **法律合规**: 免责声明内容应根据实际需求和法律要求进行调整

## 后续优化建议

1. **多语言支持**: 可以根据系统语言显示不同语言的免责声明
2. **版本控制**: 可以为免责声明添加版本号，内容更新时重新显示
3. **详细日志**: 记录用户确认免责声明的时间和IP地址
4. **自定义内容**: 允许管理员通过配置界面自定义免责声明内容
