# OCR识别功能说明

## 功能概述

AI助手现已集成OCR（光学字符识别）功能，支持：

- 📸 **图片文本识别**：识别图片中的中英文文本
- 📊 **表格识别**：识别和提取图片中的表格结构
- 📄 **增强PDF处理**：提取PDF中的图片和表格内容
- 🔍 **智能预处理**：自动优化图片质量以提高识别率

## 支持的文件格式

### 图片格式
- JPG/JPEG
- PNG
- BMP
- TIFF/TIF
- GIF

### 文档格式
- PDF（增强处理，包含图片和表格识别）
- Word文档（.docx, .doc）
- Excel表格（.xlsx, .xls）

## 使用方法

### 1. 上传图片文件
1. 在AI助手界面点击"上传文件"按钮
2. 选择支持的图片文件
3. 系统自动进行OCR识别
4. 可以询问关于图片内容的问题

### 2. PDF增强处理
1. 上传PDF文件
2. 系统自动提取文本、图片和表格
3. 对PDF中的图片进行OCR识别
4. 识别表格结构和内容

### 3. 智能问答
上传文件后，可以询问：
- "这张图片里写了什么？"
- "帮我提取图片中的文字"
- "这个表格有什么内容？"
- "总结一下这个文档的内容"

## 技术特性

### 图片预处理
- 灰度转换
- 对比度和亮度调整
- 图像锐化
- 智能缩放

### OCR识别
- 支持中文（简体/繁体）
- 支持英文
- 置信度评估
- 错误处理

### 表格识别
- 自动检测表格结构
- 提取单元格内容
- 行列信息保持
- 格式化输出

## 当前实现状态

### ✅ 已完成
- OCR服务架构设计
- 图片预处理功能
- 文件类型检测
- PDF增强处理框架
- UI界面集成
- 模拟OCR服务（用于测试）

### 🔄 待完善
- 真实OCR引擎集成（需要安装Tesseract）
- PDF图片提取优化
- 表格识别算法改进
- 识别准确率提升

## 安装真实OCR引擎

### Windows系统
1. 下载Tesseract OCR：https://github.com/UB-Mannheim/tesseract/wiki
2. 安装到默认路径：`C:\Program Files\Tesseract-OCR`
3. 添加到系统PATH环境变量
4. 下载中文语言包：
   - 简体中文：chi_sim.traineddata
   - 繁体中文：chi_tra.traineddata
5. 将语言包放到：`C:\Program Files\Tesseract-OCR\tessdata`

### 代码集成
替换`src/ocr/service.go`中的模拟实现：
```go
// 替换为真实的Tesseract调用
client := gosseract.NewClient()
defer client.Close()
client.SetLanguage("chi_sim+chi_tra+eng")
client.SetImage(imagePath)
text, err := client.Text()
```

## 使用示例

### 基本文本识别
```go
ocrService := ocr.NewOCRService()
result, err := ocrService.RecognizeImage("document.jpg")
if err == nil {
    fmt.Printf("识别文本: %s\n", result.Text)
    fmt.Printf("置信度: %.2f%%\n", result.Confidence)
}
```

### 表格识别
```go
tableResult, err := ocrService.RecognizeTable("table.png")
if err == nil {
    fmt.Printf("表格大小: %dx%d\n", tableResult.Rows, tableResult.Cols)
    for _, cell := range tableResult.Cells {
        fmt.Printf("(%d,%d): %s\n", cell.Row, cell.Column, cell.Text)
    }
}
```

### PDF增强处理
```go
pdfEnhancer := ocr.NewPDFEnhancer()
result, err := pdfEnhancer.EnhancePDF("document.pdf")
if err == nil {
    content := result.GetEnhancedContent()
    fmt.Println(content)
}
```

## 注意事项

1. **性能考虑**：OCR处理需要一定时间，大文件可能需要等待
2. **图片质量**：清晰、对比度高的图片识别效果更好
3. **语言设置**：确保设置了正确的识别语言
4. **内存使用**：处理大图片时注意内存占用
5. **错误处理**：建议添加适当的错误处理和用户提示

## 故障排除

### 常见问题
1. **识别率低**：检查图片质量，尝试调整对比度
2. **中文识别失败**：确认安装了中文语言包
3. **程序崩溃**：检查Tesseract是否正确安装
4. **文件格式不支持**：确认文件格式在支持列表中

### 日志查看
OCR相关日志会记录在应用程序日志中，可以通过日志文件查看详细错误信息。

## 未来规划

- 集成更先进的OCR引擎（如PaddleOCR）
- 支持手写文字识别
- 增加图片文字翻译功能
- 优化表格识别算法
- 支持更多图片格式
- 批量文件处理功能
