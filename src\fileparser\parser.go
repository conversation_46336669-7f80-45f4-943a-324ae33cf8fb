package fileparser

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"

	"simple_inventory_management_system/ocr"

	"github.com/h2non/filetype"
	"github.com/ledongthuc/pdf"
	"github.com/xuri/excelize/v2"
)

// FileType 文件类型枚举
type FileType int

const (
	FileTypeUnknown FileType = iota
	FileTypePDF
	FileTypeWord
	FileTypeExcel
	FileTypeImage
)

// ParseResult 解析结果
type ParseResult struct {
	Content  string   // 提取的文本内容
	FileType FileType // 文件类型
	Pages    int      // 页数（PDF）或工作表数（Excel）
	Error    error    // 解析错误
}

// FileParser 文件解析器接口
type FileParser interface {
	Parse(filePath string) (*ParseResult, error)
	SupportedTypes() []string
}

// UniversalParser 通用文件解析器
type UniversalParser struct {
	ocrService  *ocr.OCRService
	pdfEnhancer *ocr.PDFEnhancer
}

// NewUniversalParser 创建通用文件解析器
func NewUniversalParser() *UniversalParser {
	return &UniversalParser{
		ocrService:  ocr.NewOCRService(),
		pdfEnhancer: ocr.NewPDFEnhancer(),
	}
}

// Parse 解析文件
func (p *UniversalParser) Parse(filePath string) (*ParseResult, error) {
	// 检测文件类型
	fileType, err := p.detectFileType(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}

	// 根据文件类型选择解析器
	switch fileType {
	case FileTypePDF:
		return p.parsePDF(filePath)
	case FileTypeWord:
		return p.parseWord(filePath)
	case FileTypeExcel:
		return p.parseExcel(filePath)
	case FileTypeImage:
		return p.parseImage(filePath)
	default:
		err := fmt.Errorf("不支持的文件类型")
		return &ParseResult{Error: err}, err
	}
}

// detectFileType 检测文件类型
func (p *UniversalParser) detectFileType(filePath string) (FileType, error) {
	// 首先通过文件扩展名判断
	ext := strings.ToLower(filepath.Ext(filePath))
	switch ext {
	case ".pdf":
		return FileTypePDF, nil
	case ".docx", ".doc":
		return FileTypeWord, nil
	case ".xlsx", ".xls":
		return FileTypeExcel, nil
	case ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif":
		return FileTypeImage, nil
	}

	// 通过文件内容检测
	file, err := os.Open(filePath)
	if err != nil {
		return FileTypeUnknown, err
	}
	defer file.Close()

	// 读取文件头部用于类型检测
	head := make([]byte, 261)
	_, err = file.Read(head)
	if err != nil && err != io.EOF {
		return FileTypeUnknown, err
	}

	kind, err := filetype.Match(head)
	if err != nil {
		return FileTypeUnknown, err
	}

	switch kind.MIME.Type {
	case "application":
		switch kind.MIME.Subtype {
		case "pdf":
			return FileTypePDF, nil
		case "vnd.openxmlformats-officedocument.wordprocessingml.document":
			return FileTypeWord, nil
		case "vnd.openxmlformats-officedocument.spreadsheetml.sheet":
			return FileTypeExcel, nil
		}
	case "image":
		return FileTypeImage, nil
	}

	return FileTypeUnknown, fmt.Errorf("无法识别的文件类型")
}

// parsePDF 解析PDF文件（增强版，包含OCR）
func (p *UniversalParser) parsePDF(filePath string) (*ParseResult, error) {
	// 使用增强的PDF处理器
	enhancedResult, err := p.pdfEnhancer.EnhancePDF(filePath)
	if err != nil {
		// 如果增强处理失败，回退到基本PDF解析
		return p.parseBasicPDF(filePath)
	}

	// 获取完整的增强内容
	content := enhancedResult.GetEnhancedContent()

	return &ParseResult{
		Content:  content,
		FileType: FileTypePDF,
		Pages:    enhancedResult.Pages,
	}, nil
}

// parseBasicPDF 基本PDF解析（回退方案）
func (p *UniversalParser) parseBasicPDF(filePath string) (*ParseResult, error) {
	file, reader, err := pdf.Open(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}
	defer file.Close()

	var content strings.Builder
	totalPages := reader.NumPage()

	for i := 1; i <= totalPages; i++ {
		page := reader.Page(i)
		if page.V.IsNull() {
			continue
		}

		text, err := page.GetPlainText(nil)
		if err != nil {
			// 如果某页解析失败，继续处理其他页
			content.WriteString(fmt.Sprintf("[第%d页解析失败: %v]\n", i, err))
			continue
		}

		content.WriteString(text)
		content.WriteString("\n")
	}

	return &ParseResult{
		Content:  content.String(),
		FileType: FileTypePDF,
		Pages:    totalPages,
	}, nil
}

// parseImage 解析图片文件（OCR识别）
func (p *UniversalParser) parseImage(filePath string) (*ParseResult, error) {
	// 使用OCR服务识别图片中的文本
	ocrResult, err := p.ocrService.RecognizeImage(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}

	var content strings.Builder
	content.WriteString(fmt.Sprintf("=== 图片文件: %s ===\n", filepath.Base(filePath)))

	if ocrResult.Text != "" {
		content.WriteString(fmt.Sprintf("识别置信度: %.2f%%\n", ocrResult.Confidence))
		content.WriteString("识别内容:\n")
		content.WriteString(ocrResult.Text)
		content.WriteString("\n\n")
	} else {
		content.WriteString("未识别到文本内容\n")
	}

	// 尝试表格识别
	tableResult, err := p.ocrService.RecognizeTable(filePath)
	if err == nil && len(tableResult.Cells) > 0 {
		content.WriteString("=== 表格识别结果 ===\n")
		content.WriteString(fmt.Sprintf("表格大小: %d行 x %d列\n", tableResult.Rows, tableResult.Cols))

		// 按行列组织表格数据
		tableData := make(map[int]map[int]string)
		for _, cell := range tableResult.Cells {
			if tableData[cell.Row] == nil {
				tableData[cell.Row] = make(map[int]string)
			}
			tableData[cell.Row][cell.Column] = cell.Text
		}

		// 输出表格
		for row := 0; row < tableResult.Rows; row++ {
			var rowData []string
			for col := 0; col < tableResult.Cols; col++ {
				if cellText, exists := tableData[row][col]; exists {
					rowData = append(rowData, cellText)
				} else {
					rowData = append(rowData, "")
				}
			}
			content.WriteString(strings.Join(rowData, " | "))
			content.WriteString("\n")
		}
	}

	return &ParseResult{
		Content:  content.String(),
		FileType: FileTypeImage,
		Pages:    1,
	}, nil
}

// parseWord 解析Word文档
func (p *UniversalParser) parseWord(filePath string) (*ParseResult, error) {
	// 简化的Word文档解析，读取为文本
	// 注意：这是一个简化版本，实际的Word解析需要更复杂的处理
	content := fmt.Sprintf("Word文档: %s\n[注意：Word文档解析功能正在开发中，暂时无法提取详细内容]", filePath)

	return &ParseResult{
		Content:  content,
		FileType: FileTypeWord,
		Pages:    1,
	}, nil
}

// parseExcel 解析Excel文件
func (p *UniversalParser) parseExcel(filePath string) (*ParseResult, error) {
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return &ParseResult{Error: err}, err
	}
	defer file.Close()

	var content strings.Builder
	sheetList := file.GetSheetList()

	for _, sheetName := range sheetList {
		content.WriteString(fmt.Sprintf("=== 工作表: %s ===\n", sheetName))

		rows, err := file.GetRows(sheetName)
		if err != nil {
			content.WriteString(fmt.Sprintf("[工作表 %s 读取失败: %v]\n", sheetName, err))
			continue
		}

		for rowIndex, row := range rows {
			if len(row) == 0 {
				continue
			}

			// 只处理前100行，避免内容过长
			if rowIndex >= 100 {
				content.WriteString("...(内容过长，已截断)\n")
				break
			}

			content.WriteString(fmt.Sprintf("第%d行: %s\n", rowIndex+1, strings.Join(row, " | ")))
		}
		content.WriteString("\n")
	}

	return &ParseResult{
		Content:  content.String(),
		FileType: FileTypeExcel,
		Pages:    len(sheetList),
	}, nil
}

// SupportedTypes 返回支持的文件类型
func (p *UniversalParser) SupportedTypes() []string {
	return []string{".pdf", ".docx", ".doc", ".xlsx", ".xls", ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif"}
}

// GetFileTypeString 获取文件类型字符串
func GetFileTypeString(fileType FileType) string {
	switch fileType {
	case FileTypePDF:
		return "PDF"
	case FileTypeWord:
		return "Word文档"
	case FileTypeExcel:
		return "Excel表格"
	case FileTypeImage:
		return "图片"
	default:
		return "未知"
	}
}

// ValidateFile 验证文件是否可以解析
func ValidateFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("文件不存在: %s", filePath)
	}

	// 检查文件大小（限制为50MB）
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		return fmt.Errorf("无法获取文件信息: %v", err)
	}

	const maxSize = 50 * 1024 * 1024 // 50MB
	if fileInfo.Size() > maxSize {
		return fmt.Errorf("文件过大，最大支持50MB")
	}

	// 检查文件扩展名
	parser := NewUniversalParser()
	supportedTypes := parser.SupportedTypes()
	ext := strings.ToLower(filepath.Ext(filePath))

	for _, supportedExt := range supportedTypes {
		if ext == supportedExt {
			return nil
		}
	}

	return fmt.Errorf("不支持的文件类型: %s，支持的类型: %s", ext, strings.Join(supportedTypes, ", "))
}
