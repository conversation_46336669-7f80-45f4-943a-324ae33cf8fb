package ui

import (
	"bytes"
	"fmt"
	"image"
	"log"
	"os"
	"path/filepath"
	"simple_inventory_management_system/api"
	"simple_inventory_management_system/internal/auth"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
)

const (
	AppTitle                       = "生活必需品流通保供监测"
	SystemName                     = "生活必需品流通保供数据上报系统"
	PageSize                       = 20
	ERROR_MSG_COLUMN_WIDTH float32 = 500
	DEFAULT_ROW_HEIGHT     float32 = 28
)

type App struct {
	fyneApp     fyne.App
	mainWin     fyne.Window
	user        *api.User
	refreshTabs map[string]func() // 存储各标签页的刷新函数
}

var iconData []byte

func NewApp() *App {
	fontPath := EnsureMicrosoftYaheiFont()

	a := app.NewWithID("")
	a.Settings().SetTheme(NewMyTheme(fontPath))

	// 初始化资源
	// InitResources()

	// 设置应用程序图标
	// SetAppIcon(a)

	return &App{
		fyneApp: a,
	}
}

func (a *App) Run() {
	// 加载图标数据
	iconData = loadIconData()
	logger.Init()
	err := database.InitDB()
	if err != nil {
		logger.Log.Fatal("数据库初始化失败！", err)
	}
	companyName, _ := database.GetConfig("companyName")
	if companyName != "" {
		a.mainWin = a.fyneApp.NewWindow(companyName)
	} else {
		a.mainWin = a.fyneApp.NewWindow(AppTitle)
	}

	// 窗口创建后设置图标
	if len(iconData) > 0 && validateIconData(iconData) {
		setWindowIcon(a.mainWin, iconData)
	}
	// 设置应用图标（系统托盘/任务栏）
	if len(iconData) > 0 && validateIconData(iconData) {
		setAppIcon(a.fyneApp, iconData)
		// 注意：窗口图标需要在窗口创建后设置，这里先保存数据
		logger.Log.Println("图标数据已加载，将在窗口创建后设置")
	} else {
		logger.Log.Println("图标数据无效，跳过图标设置")
	}

	if a.refreshTabs == nil {
		a.refreshTabs = make(map[string]func())
	}
	a.showLoginScreen()
	a.mainWin.ShowAndRun()
}

// handleLogin 已被内联到showLoginScreen中，保留此函数以兼容可能的外部调用
func (a *App) handleLogin(username, password string) {
	// 显示登录中对话框
	progress := dialog.NewProgress("登录", "正在验证用户信息...", a.mainWin)
	progress.Show()

	// 在goroutine中处理登录
	go func() {
		// 执行登录
		user, err := auth.AuthenticateUser(username, password)

		// 在主线程中更新UI
		go func() {
			// 关闭进度对话框
			progress.Hide()

			if err != nil {
				// 登录失败，显示错误信息
				dialog.ShowError(err, a.mainWin)
			} else {
				// 登录成功，切换到主界面
				a.user = user
				a.showMainScreen()
			}
		}()
	}()
}

func (a *App) showLoginScreen() {
	// 重置Canvas的键盘事件处理，避免多个事件处理器重叠
	a.mainWin.Canvas().SetOnTypedKey(nil)

	// 创建带有固定宽度的用户名输入框
	usernameEntry := newFixedWidthEntry(200)
	usernameEntry.SetPlaceHolder("用户名")

	// 创建密码输入框
	passwordEntry := widget.NewPasswordEntry()
	passwordEntry.SetPlaceHolder("密码")

	// 创建登录状态标签（初始隐藏）
	statusLabel := widget.NewLabel("")
	statusLabel.Hide()

	var form *widget.Form
	// 创建登录表单
	form = &widget.Form{
		Items: []*widget.FormItem{
			{Text: "用户名", Widget: usernameEntry},
			{Text: "密码", Widget: passwordEntry},
		},
		OnSubmit: func() {
			// 禁用表单，显示登录中状态
			form.Disable()
			statusLabel.SetText("登录中...")
			statusLabel.Show()

			// 在goroutine中处理登录，避免阻塞UI
			go func() {
				// 执行登录
				user, err := auth.AuthenticateUser(usernameEntry.Text, passwordEntry.Text)

				// 在主线程中更新UI
				if err != nil {
					// 登录失败，显示错误信息
					statusLabel.SetText(fmt.Sprintf("登录失败: %v", err))
					statusLabel.Show()
					form.Enable()
				} else {
					// 登录成功，切换到主界面
					a.user = user
					a.showMainScreen()
				}
			}()
		},
		SubmitText: "登录",
	}

	// 设置回车键提交表单
	setupReturnKeySubmit(a.mainWin.Canvas(), form.OnSubmit, usernameEntry, passwordEntry)

	// 创建登录界面布局
	// logoWidget := GetLogoWidget()

	// 创建VBox的子组件列表
	vboxItems := []fyne.CanvasObject{}

	// 安全地添加logo组件
	// if logoWidget != nil {
	// 	vboxItems = append(vboxItems, logoWidget)
	// }

	// 添加其他组件
	vboxItems = append(vboxItems,
		widget.NewLabel(SystemName),
		form,
		statusLabel,
	)

	content := container.NewCenter(
		container.NewVBox(vboxItems...),
	)

	// 设置窗口内容
	a.mainWin.SetContent(content)

	// 调整窗口大小并居中显示，然后设置焦点
	go func() {
		time.Sleep(50 * time.Millisecond)
		a.mainWin.Resize(fyne.NewSize(400, 200))
		a.mainWin.CenterOnScreen()

		// 设置焦点到用户名输入框
		a.mainWin.Canvas().Focus(usernameEntry)
	}()
}

func setupReturnKeySubmit(canvas fyne.Canvas, f func(), entries ...fyne.Focusable) {
	// 设置全局回车键监听
	canvas.SetOnTypedKey(func(ke *fyne.KeyEvent) {
		if ke.Name == fyne.KeyEnter || ke.Name == fyne.KeyReturn {
			focused := canvas.Focused()
			// 检查当前焦点是否在指定的输入框中
			for _, entry := range entries {
				if entry == focused {
					f()
					return
				}
			}
		}
	})

	// 为每个输入框单独设置 OnSubmitted 事件
	if len(entries) >= 2 {
		// 假设第一个是用户名，第二个是密码
		if usernameEntry, ok := entries[0].(*widget.Entry); ok {
			usernameEntry.OnSubmitted = func(text string) {
				// 用户名输入框按回车，焦点移到密码输入框
				canvas.Focus(entries[1])
			}
		}

		if passwordEntry, ok := entries[1].(*widget.Entry); ok {
			passwordEntry.OnSubmitted = func(text string) {
				// 密码输入框按回车，执行登录
				f()
			}
		}
	}
}

func (a *App) showMainScreen() {
	// 重置Canvas的键盘事件处理，避免多个事件处理器重叠
	a.mainWin.Canvas().SetOnTypedKey(nil)

	// 显示加载中对话框
	progress := dialog.NewCustomWithoutButtons("加载中", container.NewVBox(
		widget.NewLabel("正在加载应用程序..."),
		widget.NewProgressBarInfinite(),
	), a.mainWin)
	progress.Show()

	// 在goroutine中创建UI组件，避免阻塞主线程
	go func() {
		// 创建标签页
		tabs := container.NewAppTabs(
			container.NewTabItem("数据上报", a.createSubmissionTab()),
			container.NewTabItem("上报历史", a.createHistoryTab()),
			container.NewTabItem("模板下载", a.createTemplatesTab()),
			container.NewTabItem("行政区划", a.createAdminDivisionsTab()),
			container.NewTabItem("地区代码", a.createRegionsTab()),
			container.NewTabItem("品类管理", a.createCategoriesTab()),
			container.NewTabItem("AI助手", a.createAIChatTab()),
		)

		// 确保只有管理员才能看到和访问管理选项卡
		if a.user != nil && a.user.Role == "管理员" {
			tabs.Append(container.NewTabItem("数据库管理", a.createDatabaseManagementTab()))
			tabs.Append(container.NewTabItem("配置管理", a.createConfigTab()))
			tabs.Append(container.NewTabItem("系统管理", a.createSystemManagementTab()))
		}

		// 创建登出按钮
		logoutButton := widget.NewButton("登出", func() {
			// 显示确认对话框
			dialog.ShowConfirm("确认登出", "确定要退出登录吗？", func(confirm bool) {
				if confirm {
					// 在登出前清理资源
					a.user = nil
					// 使用主线程调用showLoginScreen，避免并发问题
					a.showLoginScreen()
				}
			}, a.mainWin)
		})

		// 设置标签切换事件
		tabs.OnSelected = func(tab *container.TabItem) {
			if refreshFunc, ok := a.refreshTabs[tab.Text]; ok {
				refreshFunc()
			}
		}

		// 创建头部区域
		header := container.NewHBox(
			widget.NewLabel(fmt.Sprintf("欢迎, %s (%s)", a.user.Username, a.user.Role)),
			logoutButton,
		)

		// 创建主界面布局
		content := container.NewBorder(header, nil, nil, nil, tabs)

		// 在主线程中更新UI
		go func() {
			// 隐藏加载对话框
			progress.Hide()

			// 设置窗口内容
			a.mainWin.SetContent(content)

			// 调整窗口大小并居中显示
			a.mainWin.Resize(fyne.NewSize(1024, 768))
			a.mainWin.CenterOnScreen()
		}()
	}()
}

// 设置应用图标（系统托盘/任务栏）
func setAppIcon(a fyne.App, data []byte) {
	// 转换为 image 对象
	_, _, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		log.Println("Error decoding icon:", err)
		return
	}

	// 设置应用图标
	a.SetIcon(fyne.NewStaticResource("app-icon", data))

	// 系统托盘图标（如果支持）- 暂时禁用以避免错误
	// if desk, ok := a.(desktop.App); ok {
	//     desk.SetSystemTrayIcon(fyne.NewStaticResource("tray-icon", data))
	// }
}

// 设置窗口图标
func setWindowIcon(w fyne.Window, data []byte) {
	// 检查窗口是否为nil
	if w == nil {
		log.Println("窗口对象为nil，无法设置图标")
		return
	}

	// 检查数据是否有效
	if len(data) == 0 {
		log.Println("图标数据为空，无法设置图标")
		return
	}

	// 转换为 image 对象
	_, _, err := image.Decode(bytes.NewReader(data))
	if err != nil {
		log.Println("Error decoding window icon:", err)
		return
	}

	// 设置窗口图标
	w.SetIcon(fyne.NewStaticResource("window-icon", data))
	log.Println("窗口图标设置成功")
}

// loadIconData 加载图标数据
func loadIconData() []byte {
	// 优先尝试加载ICO格式的图标
	icoFiles := []string{
		"logo/logo_24x24.ico",
		"logo/logo_16x16.ico",
		"logo/app.ico",
	}
	icoPath := filepath.Join("assets", "logo.png")
	_, err := os.Stat(icoPath)
	if err != nil {
		logger.Log.WithField("path", icoPath).Error("图标文件不存在")
	}
	data, err := os.ReadFile(icoPath)
	if err == nil {
		logger.Log.WithFields(logrus.Fields{
			"path": icoPath,
		}).Info("成功加载ICO图标")
		return data
	}

	for _, path := range icoFiles {
		if data, err := os.ReadFile(path); err == nil {
			log.Printf("成功加载ICO图标: %s", path)
			return data
		}
	}

	// 如果没有ICO文件，尝试加载PNG文件
	pngFiles := []string{
		"logo/logo.png",
		"logo/icon.png",
	}

	for _, path := range pngFiles {
		if data, err := os.ReadFile(path); err == nil {
			log.Printf("成功加载PNG图标: %s", path)
			// PNG文件可以直接用作图标
			return data
		}
	}

	log.Println("未找到图标文件，使用默认图标")
	return nil
}

// validateIconData 验证图标数据是否有效
func validateIconData(data []byte) bool {
	if len(data) == 0 {
		return false
	}

	// 尝试解码图像以验证格式
	_, _, err := image.Decode(bytes.NewReader(data))
	return err == nil
}

// func (a *App) safeResizeAndCenter(size fyne.Size) {
// 	// 在调整大小和居中之前添加一个小的延迟
// 	// 有时，特别是在Windows上，立即调整大小可能会导致窗口位置不正确
// 	// time.Sleep(50 * time.Millisecond)
// 	a.mainWin.Resize(size)
// 	a.mainWin.CenterOnScreen()
// }
