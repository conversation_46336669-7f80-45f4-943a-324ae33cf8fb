package fileparser

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/xuri/excelize/v2"
)

// ConvertResult 转换结果
type ConvertResult struct {
	OutputPath string // 输出文件路径
	Success    bool   // 转换是否成功
	Error      error  // 转换错误
}

// FileConverter 文件转换器
type FileConverter struct {
	parser *UniversalParser
}

// NewFileConverter 创建文件转换器
func NewFileConverter() *FileConverter {
	return &FileConverter{
		parser: NewUniversalParser(),
	}
}

// ConvertPDFToWord 将PDF转换为Word文档
func (c *FileConverter) ConvertPDFToWord(pdfPath, outputDir string) (*ConvertResult, error) {
	// 解析PDF内容
	result, err := c.parser.parsePDF(pdfPath)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	// 生成输出文件名
	baseName := strings.TrimSuffix(filepath.Base(pdfPath), filepath.Ext(pdfPath))
	outputPath := filepath.Join(outputDir, baseName+"_converted.docx")

	// 创建Word文档内容
	content := result.Content

	// 保存为文本文件（简化版本，实际的PDF转Word需要更复杂的处理）
	// 这里我们先创建一个简单的文本文件，后续可以扩展为真正的Word格式
	err = os.WriteFile(outputPath, []byte(content), 0644)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	return &ConvertResult{
		OutputPath: outputPath,
		Success:    true,
	}, nil
}

// ConvertExcelToText 将Excel转换为文本文件
func (c *FileConverter) ConvertExcelToText(excelPath, outputDir string) (*ConvertResult, error) {
	// 解析Excel内容
	result, err := c.parser.parseExcel(excelPath)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	// 生成输出文件名
	baseName := strings.TrimSuffix(filepath.Base(excelPath), filepath.Ext(excelPath))
	outputPath := filepath.Join(outputDir, baseName+"_converted.txt")

	// 保存为文本文件
	err = os.WriteFile(outputPath, []byte(result.Content), 0644)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	return &ConvertResult{
		OutputPath: outputPath,
		Success:    true,
	}, nil
}

// ConvertWordToText 将Word文档转换为文本文件
func (c *FileConverter) ConvertWordToText(wordPath, outputDir string) (*ConvertResult, error) {
	// 解析Word内容
	result, err := c.parser.parseWord(wordPath)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	// 生成输出文件名
	baseName := strings.TrimSuffix(filepath.Base(wordPath), filepath.Ext(wordPath))
	outputPath := filepath.Join(outputDir, baseName+"_converted.txt")

	// 保存为文本文件
	err = os.WriteFile(outputPath, []byte(result.Content), 0644)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	return &ConvertResult{
		OutputPath: outputPath,
		Success:    true,
	}, nil
}

// ConvertToExcel 将文本内容转换为Excel文件
func (c *FileConverter) ConvertToExcel(content, outputPath string) (*ConvertResult, error) {
	file := excelize.NewFile()
	defer file.Close()

	// 创建工作表
	sheetName := "转换内容"
	index, err := file.NewSheet(sheetName)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	// 将内容按行分割
	lines := strings.Split(content, "\n")

	// 写入Excel
	for i, line := range lines {
		if i >= 1000 { // 限制行数，避免文件过大
			break
		}

		cell := fmt.Sprintf("A%d", i+1)
		err = file.SetCellValue(sheetName, cell, line)
		if err != nil {
			continue // 忽略单个单元格的错误
		}
	}

	// 设置活动工作表
	file.SetActiveSheet(index)

	// 删除默认的Sheet1
	err = file.DeleteSheet("Sheet1")
	if err != nil {
		// 忽略删除默认工作表的错误
	}

	// 保存文件
	err = file.SaveAs(outputPath)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	return &ConvertResult{
		OutputPath: outputPath,
		Success:    true,
	}, nil
}

// AutoConvert 自动转换文件（根据文件类型选择合适的转换方式）
func (c *FileConverter) AutoConvert(inputPath, outputDir string, targetFormat string) (*ConvertResult, error) {
	// 确保输出目录存在
	err := os.MkdirAll(outputDir, 0755)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	// 检测输入文件类型
	fileType, err := c.parser.detectFileType(inputPath)
	if err != nil {
		return &ConvertResult{Success: false, Error: err}, err
	}

	// 根据源文件类型和目标格式进行转换
	switch fileType {
	case FileTypePDF:
		switch strings.ToLower(targetFormat) {
		case "word", "docx":
			return c.ConvertPDFToWord(inputPath, outputDir)
		case "text", "txt":
			// PDF转文本：先解析内容，再保存为文本
			result, err := c.parser.parsePDF(inputPath)
			if err != nil {
				return &ConvertResult{Success: false, Error: err}, err
			}
			baseName := strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))
			outputPath := filepath.Join(outputDir, baseName+"_converted.txt")
			err = os.WriteFile(outputPath, []byte(result.Content), 0644)
			if err != nil {
				return &ConvertResult{Success: false, Error: err}, err
			}
			return &ConvertResult{OutputPath: outputPath, Success: true}, nil
		default:
			return &ConvertResult{Success: false, Error: fmt.Errorf("PDF不支持转换为%s格式", targetFormat)}, nil
		}

	case FileTypeWord:
		switch strings.ToLower(targetFormat) {
		case "text", "txt":
			return c.ConvertWordToText(inputPath, outputDir)
		default:
			return &ConvertResult{Success: false, Error: fmt.Errorf("Word文档不支持转换为%s格式", targetFormat)}, nil
		}

	case FileTypeExcel:
		switch strings.ToLower(targetFormat) {
		case "text", "txt":
			return c.ConvertExcelToText(inputPath, outputDir)
		default:
			return &ConvertResult{Success: false, Error: fmt.Errorf("Excel文件不支持转换为%s格式", targetFormat)}, nil
		}

	default:
		return &ConvertResult{Success: false, Error: fmt.Errorf("不支持的源文件类型")}, nil
	}
}

// GetSupportedConversions 获取支持的转换类型
func (c *FileConverter) GetSupportedConversions() map[string][]string {
	return map[string][]string{
		"PDF":   {"Word", "文本"},
		"Word":  {"文本"},
		"Excel": {"文本"},
	}
}
