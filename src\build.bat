@echo off
echo 正在构建应用程序...

REM 设置构建参数
set APP_NAME=inventory_management
set VERSION=1.0.0

REM 清理之前的构建
if exist %APP_NAME%.exe del %APP_NAME%.exe

REM 构建应用程序
echo 编译Go程序...
go build -ldflags "-s -w -H windowsgui" -o %APP_NAME%.exe ./cmd

if %ERRORLEVEL% NEQ 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！

REM 创建发布目录
if not exist "release" mkdir release
if exist "release\*" del /q "release\*"

REM 复制可执行文件
copy %APP_NAME%.exe release\

REM 复制资源文件
echo 复制资源文件...
if exist "logo" (
    if not exist "release\logo" mkdir "release\logo"
    copy logo\*.* release\logo\
)

REM 复制其他必要文件
if exist "fonts" (
    if not exist "release\fonts" mkdir "release\fonts"
    copy fonts\*.* release\fonts\
)

if exist "web_ui" (
    if not exist "release\web_ui" mkdir "release\web_ui"
    xcopy web_ui release\web_ui\ /E /I /Q
)

echo.
echo 构建完成！
echo 可执行文件: release\%APP_NAME%.exe
echo 资源文件已复制到 release 目录
echo.
pause
