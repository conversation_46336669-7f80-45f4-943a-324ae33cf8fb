{"file":"F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/logger/logger.go:37","func":"simple_inventory_management_system/internal/logger.Init","level":"info","msg":"日志系统初始化成功","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"roles\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[115.234ms] [rows:0] CREATE TABLE `roles` (`id` integer PRIMARY KEY AUTOINCREMENT,`name` text NOT NULL,CONSTRAINT `uni_roles_name` UNIQUE (`name`))","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[2.670ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"users\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[9.376ms] [rows:0] CREATE TABLE `users` (`id` integer PRIMARY KEY AUTOINCREMENT,`username` text NOT NULL,`password_hash` text NOT NULL,`role_id` integer NOT NULL,`created_at` datetime DEFAULT CURRENT_TIMESTAMP,CONSTRAINT `fk_roles_users` FOREIGN KEY (`role_id`) REFERENCES `roles`(`id`),CONSTRAINT `uni_users_username` UNIQUE (`username`))","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"configs\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[7.315ms] [rows:0] CREATE TABLE `configs` (`key` text,`value` text NOT NULL,`created_at` datetime DEFAULT CURRENT_TIMESTAMP,PRIMARY KEY (`key`))","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"categories\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[5.812ms] [rows:0] CREATE TABLE `categories` (`id` integer PRIMARY KEY AUTOINCREMENT,`category_code` text NOT NULL,`category_name` text NOT NULL,`category_type` text,`parent_code` text,`industry_type` text,`price_unit` text,`quantity_unit` text,`created_at` datetime DEFAULT CURRENT_TIMESTAMP)","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"regions\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[4.372ms] [rows:0] CREATE TABLE `regions` (`id` integer PRIMARY KEY AUTOINCREMENT,`code` text NOT NULL,`name` text NOT NULL,`is_deleted` integer DEFAULT 0,`created_at` datetime DEFAULT CURRENT_TIMESTAMP,CONSTRAINT `uni_regions_code` UNIQUE (`code`))","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"admin_divisions\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[4.695ms] [rows:0] CREATE TABLE `admin_divisions` (`id` integer PRIMARY KEY AUTOINCREMENT,`code` text NOT NULL,`unit_name` text NOT NULL,`is_deleted` integer DEFAULT 0,`created_at` datetime DEFAULT CURRENT_TIMESTAMP,CONSTRAINT `uni_admin_divisions_code` UNIQUE (`code`))","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"submissions\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[3.524ms] [rows:0] CREATE TABLE `submissions` (`id` integer PRIMARY KEY AUTOINCREMENT,`user_id` integer NOT NULL,`report_type` text NOT NULL,`file_path` text NOT NULL,`response_code` integer,`response_msg` text,`request_id` text,`start_time` datetime,`end_time` datetime,`result_time` datetime,`status` text,`error_msg` text,`created_at` datetime DEFAULT CURRENT_TIMESTAMP,CONSTRAINT `fk_submissions_user` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`))","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"report_category_associations\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[4.545ms] [rows:0] CREATE TABLE `report_category_associations` (`id` integer PRIMARY KEY AUTOINCREMENT,`report_type` text NOT NULL,`category_code` text NOT NULL,`created_at` datetime DEFAULT CURRENT_TIMESTAMP)","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `roles` WHERE `roles`.`name` = \"管理员\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[7.482ms] [rows:1] INSERT INTO `roles` (`name`) VALUES (\"管理员\") RETURNING `id`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.547ms] [rows:0] SELECT * FROM `roles` WHERE `roles`.`name` = \"普通员工\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[3.543ms] [rows:1] INSERT INTO `roles` (`name`) VALUES (\"普通员工\") RETURNING `id`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `roles` WHERE `roles`.`name` = \"测试账号\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[24.472ms] [rows:1] INSERT INTO `roles` (`name`) VALUES (\"测试账号\") RETURNING `id`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:301","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `users` WHERE username = \"admin\" ORDER BY `users`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:304","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `roles` WHERE name = \"管理员\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:306","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[16.409ms] [rows:1] INSERT INTO `users` (`username`,`password_hash`,`role_id`) VALUES (\"admin\",\"$2a$10$6wNbxqUo4e01YbhKrS.ApOJs.JvJsNNvRWqFJ1sTnpxPb9pPQV/12\",1) RETURNING `created_at`,`id`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"loginName\" AND `configs`.`value` = \"admin\") AND `configs`.`key` = \"loginName\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[31.656ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"loginName\",\"admin\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"linkman\" AND `configs`.`value` = \"测试填报人\") AND `configs`.`key` = \"linkman\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[11.266ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"linkman\",\"测试填报人\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"leader\" AND `configs`.`value` = \"测试单位负责人\") AND `configs`.`key` = \"leader\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[5.774ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"leader\",\"测试单位负责人\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.520ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"statistician\" AND `configs`.`value` = \"测试统计负责人\") AND `configs`.`key` = \"statistician\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[6.489ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"statistician\",\"测试统计负责人\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"report_labels\" AND `configs`.`value` = \"3.2,3.5\") AND `configs`.`key` = \"report_labels\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[5.231ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"report_labels\",\"3.2,3.5\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"secretId\" AND `configs`.`value` = \"your_secret_id\") AND `configs`.`key` = \"secretId\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[3.867ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"secretId\",\"your_secret_id\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"secretKey\" AND `configs`.`value` = \"your_secret_key\") AND `configs`.`key` = \"secretKey\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[5.309ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"secretKey\",\"your_secret_key\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"companyName\" AND `configs`.`value` = \"测试公司\") AND `configs`.`key` = \"companyName\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"companyName\",\"测试公司\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"telephone\" AND `configs`.`value` = \"13888888888\") AND `configs`.`key` = \"telephone\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[11.366ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"telephone\",\"13888888888\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"baseURL\" AND `configs`.`value` = \"http://58.48.136.183:19090/hbyjbg-api/model/v2/\") AND `configs`.`key` = \"baseURL\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[3.457ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"baseURL\",\"http://58.48.136.183:19090/hbyjbg-api/model/v2/\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:0] SELECT * FROM `configs` WHERE (`configs`.`key` = \"openrouter_api_key\" AND `configs`.`value` = \"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\") AND `configs`.`key` = \"openrouter_api_key\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[6.527ms] [rows:1] INSERT INTO `configs` (`key`,`value`) VALUES (\"openrouter_api_key\",\"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\") RETURNING `created_at`","time":"2025-07-06T11:39:09+08:00"}
{"file":"F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/ui/app.go:374","func":"simple_inventory_management_system/ui.loadIconData","level":"error","msg":"图标文件不存在","path":"assets\\logo.png","time":"2025-07-06T11:39:09+08:00"}
{"file":"F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/logger/logger.go:37","func":"simple_inventory_management_system/internal/logger.Init","level":"info","msg":"日志系统初始化成功","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"roles\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"roles\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `roles` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"users\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"users\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `users` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type = \"table\" AND tbl_name = \"users\" AND (sql LIKE \"%CONSTRAINT \"\"fk_roles_users\"\" %\" OR sql LIKE \"%CONSTRAINT fk_roles_users %\" OR sql LIKE \"%CONSTRAINT `fk_roles_users`%\" OR sql LIKE \"%CONSTRAINT [fk_roles_users]%\" OR sql LIKE \"%CONSTRAINT \tfk_roles_users\t%\")","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"configs\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"configs\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `configs` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"categories\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"categories\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `categories` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"regions\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"regions\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `regions` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"admin_divisions\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"admin_divisions\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `admin_divisions` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"submissions\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"submissions\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `submissions` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type = \"table\" AND tbl_name = \"submissions\" AND (sql LIKE \"%CONSTRAINT \"\"fk_submissions_user\"\" %\" OR sql LIKE \"%CONSTRAINT fk_submissions_user %\" OR sql LIKE \"%CONSTRAINT `fk_submissions_user`%\" OR sql LIKE \"%CONSTRAINT [fk_submissions_user]%\" OR sql LIKE \"%CONSTRAINT \tfk_submissions_user\t%\")","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT count(*) FROM sqlite_master WHERE type='table' AND name=\"report_category_associations\"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT sql FROM sqlite_master WHERE type IN (\"table\",\"index\") AND tbl_name = \"report_category_associations\" AND sql IS NOT NULL order by type = \"table\" desc","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:78","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:-] SELECT * FROM `report_category_associations` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `roles` WHERE `roles`.`name` = \"管理员\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `roles` WHERE `roles`.`name` = \"普通员工\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:296","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `roles` WHERE `roles`.`name` = \"测试账号\" ORDER BY `roles`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:301","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `users` WHERE username = \"admin\" ORDER BY `users`.`id` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"report_labels\" AND `configs`.`value` = \"3.2,3.5\") AND `configs`.`key` = \"report_labels\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"baseURL\" AND `configs`.`value` = \"http://58.48.136.183:19090/hbyjbg-api/model/v2/\") AND `configs`.`key` = \"baseURL\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"secretId\" AND `configs`.`value` = \"your_secret_id\") AND `configs`.`key` = \"secretId\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"secretKey\" AND `configs`.`value` = \"your_secret_key\") AND `configs`.`key` = \"secretKey\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"openrouter_api_key\" AND `configs`.`value` = \"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\") AND `configs`.`key` = \"openrouter_api_key\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[1.516ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"loginName\" AND `configs`.`value` = \"admin\") AND `configs`.`key` = \"loginName\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"companyName\" AND `configs`.`value` = \"测试公司\") AND `configs`.`key` = \"companyName\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"leader\" AND `configs`.`value` = \"测试单位负责人\") AND `configs`.`key` = \"leader\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"statistician\" AND `configs`.`value` = \"测试统计负责人\") AND `configs`.`key` = \"statistician\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"linkman\" AND `configs`.`value` = \"测试填报人\") AND `configs`.`key` = \"linkman\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:324","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE (`configs`.`key` = \"telephone\" AND `configs`.`value` = \"13888888888\") AND `configs`.`key` = \"telephone\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"2025/07/06 11:39:09 F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/internal/database/database.go:124","time":"2025-07-06T11:39:09+08:00"}
{"level":"info","msg":"[0.000ms] [rows:1] SELECT * FROM `configs` WHERE key = \"companyName\" ORDER BY `configs`.`key` LIMIT 1","time":"2025-07-06T11:39:09+08:00"}
{"file":"F:/zql/work/workspaces/goWorkspace/simple_inventory_management_system/src/ui/app.go:83","func":"simple_inventory_management_system/ui.(*App).Run","level":"info","msg":"图标数据无效，跳过图标设置","time":"2025-07-06T11:39:09+08:00"}
