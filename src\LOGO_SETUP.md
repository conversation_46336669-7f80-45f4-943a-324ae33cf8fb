# Logo和图标设置指南

## 概述
本系统支持自定义logo和应用程序图标，提供了多种方式来集成资源文件。

## 文件结构
```
src/
├── logo/                    # logo资源目录
│   ├── logo.png            # 主logo图片
│   └── logo_16x16.ico      # 应用程序图标
├── ui/
│   ├── resources.go        # 资源管理
│   ├── logo_resource.go    # 生成的logo资源（可选）
│   └── icon_resource.go    # 生成的图标资源（可选）
├── build.bat              # 构建脚本
└── generate_resources.bat # 资源生成脚本
```

## 使用方法

### 方法1：直接使用文件（推荐用于开发）
1. 将logo图片放在 `logo/logo.png`
2. 将图标文件放在 `logo/logo_16x16.ico`
3. 运行程序，系统会自动尝试加载这些文件

### 方法2：嵌入资源（推荐用于发布）
1. 运行 `generate_resources.bat` 生成资源文件
2. 这会创建 `ui/logo_resource.go` 和 `ui/icon_resource.go`
3. 修改 `ui/resources.go` 使用生成的资源
4. 运行 `build.bat` 构建应用程序

### 方法3：发布时复制文件
1. 使用 `build.bat` 构建应用程序
2. 脚本会自动将logo目录复制到release目录
3. 发布时确保logo目录与可执行文件在同一目录

## 图片要求

### Logo图片 (logo.png)
- 格式：PNG
- 推荐尺寸：64x64 像素或更大
- 背景：透明背景效果更佳
- 用途：显示在登录界面和AI聊天界面

### 应用图标
支持多种格式和尺寸：

#### ICO格式（推荐用于应用图标）
- `logo_16x16.ico` - 16x16 像素
- `logo_24x24.ico` - 24x24 像素
- `logo_32x32.ico` - 32x32 像素

#### PNG格式（可用作图标）
- `logo.png` - 任意尺寸，系统会自动缩放
- 推荐尺寸：16x16, 24x24, 32x32, 64x64

### 图标加载优先级
1. **ICO文件**：优先加载ICO格式的图标文件
2. **PNG文件**：如果没有ICO文件，使用PNG文件作为图标

### 用途说明
- **应用程序窗口图标**：显示在窗口标题栏
- **任务栏图标**：显示在Windows任务栏
- **系统托盘图标**：显示在系统托盘（如果支持）

## 构建和发布

### 开发环境
```bash
# 直接运行
go run ./cmd

# 或者构建
go build ./cmd
```

### 生产环境
```bash
# 1. 生成嵌入资源（可选）
generate_resources.bat

# 2. 构建发布版本
build.bat
```

### 发布包内容
发布时确保包含以下文件：
- `inventory_management.exe` - 主程序
- `logo/` - logo资源目录（如果使用文件方式）
- `fonts/` - 字体文件目录（如果有）

## 自定义logo

### 替换logo
1. 准备您的logo图片（PNG格式，建议64x64或128x128）
2. 将文件重命名为 `logo.png`
3. 放置在 `logo/` 目录下
4. 重新构建应用程序

### 替换图标
1. 准备您的图标文件（ICO格式，16x16）
2. 将文件重命名为 `logo_16x16.ico`
3. 放置在 `logo/` 目录下
4. 重新构建应用程序

## 故障排除

### Logo不显示
1. 检查文件路径是否正确
2. 检查文件格式是否支持
3. 检查文件权限是否正确
4. 查看控制台是否有错误信息

### 图标不显示
1. 确保ICO文件格式正确
2. 检查文件大小（建议小于100KB）
3. 尝试使用标准的16x16尺寸

### 构建问题
1. 确保Go环境正确安装
2. 确保fyne工具已安装：`go install fyne.io/fyne/v2/cmd/fyne@latest`
3. 检查文件路径和权限

## 技术说明

### 资源加载优先级
1. 嵌入的资源文件（如果存在）
2. 相对路径的文件
3. 默认文本标签

### 支持的格式
- Logo：PNG, JPG, GIF
- 图标：ICO, PNG

### 性能考虑
- 嵌入资源会增加可执行文件大小
- 外部文件方式更灵活，但需要确保文件存在
- 建议logo文件大小控制在500KB以内
