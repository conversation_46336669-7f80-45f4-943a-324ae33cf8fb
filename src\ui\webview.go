package ui

import (
	"fmt"
	"os"
	"path/filepath"
	"simple_inventory_management_system/ai"
	"simple_inventory_management_system/fileparser"
	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"
	"strings"
	"time"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
)

// WebView 是一个自定义的WebView组件
type WebView struct {
	widget.BaseWidget
	url            string
	content        fyne.CanvasObject
	window         fyne.Window             // 添加窗口引用用于显示通知
	chatHistory    []ai.Message            // 对话历史记录
	maxHistorySize int                     // 最大历史记录数量
	fileManager    *fileparser.FileManager // 文件管理器
	searchEnhancer *ai.SearchEnhancer      // 搜索增强器
}

// NewWebView 创建一个新的WebView实例
func NewWebView() *WebView {
	// 创建文件上传目录
	uploadDir := filepath.Join("uploads", "ai_files")
	os.MkdirAll(uploadDir, 0755)

	w := &WebView{
		chatHistory:    make([]ai.Message, 0),
		maxHistorySize: 40, // 最大记住20个问答（每个问答包含用户消息和AI回复，共40条消息）
		fileManager:    fileparser.NewFileManager(uploadDir),
		searchEnhancer: ai.NewSearchEnhancer(),
	}
	w.ExtendBaseWidget(w)

	// 初始化时显示加载提示
	w.content = container.NewCenter(
		container.NewVBox(
			widget.NewLabel("AI对话助手"),
			widget.NewLabel("正在加载..."),
		),
	)

	return w
}

// SetWindow 设置窗口引用
func (w *WebView) SetWindow(window fyne.Window) {
	w.window = window
}

// addToHistory 添加消息到历史记录
func (w *WebView) addToHistory(role, content string) {
	message := ai.Message{
		Role:    role,
		Content: content,
	}

	w.chatHistory = append(w.chatHistory, message)

	// 如果超过最大历史记录数量，删除最旧的记录
	if len(w.chatHistory) > w.maxHistorySize {
		// 保留最新的记录，删除最旧的
		w.chatHistory = w.chatHistory[len(w.chatHistory)-w.maxHistorySize:]

		logger.Log.WithFields(logrus.Fields{
			"removed_old_messages": len(w.chatHistory) - w.maxHistorySize,
			"current_count":        len(w.chatHistory),
		}).Info("删除旧的历史记录以保持在限制范围内")
	}

	logger.Log.WithFields(logrus.Fields{
		"history_count": len(w.chatHistory),
		"role":          role,
		"content_len":   len(content),
	}).Debug("添加消息到历史记录")
}

// clearHistory 清空历史记录
func (w *WebView) clearHistory() {
	w.chatHistory = make([]ai.Message, 0)
	logger.Log.Info("对话历史记录已清空")
}

// getHistoryWithSystemPrompt 获取包含系统提示的完整历史记录
func (w *WebView) getHistoryWithSystemPrompt() []ai.Message {
	systemMessage := ai.Message{
		Role: "system",
		Content: `你是一个专业的AI助手，请用中文回答用户的问题。你可以帮助用户了解系统功能、解答使用问题、提供技术支持。

特别功能：
1. 文件处理：我可以帮助用户分析上传的PDF、Word、Excel文件
2. 内容总结：对文件内容进行总结和分析
3. 格式转换：协助用户进行文件格式转换
4. 数据提取：从文件中提取关键信息

当用户提供文件内容时，请：
- 仔细分析文件内容
- 提供准确的总结和分析
- 回答用户关于文件的具体问题
- 如果需要，建议合适的处理方式

请保持友好、专业的语调，记住之前的对话内容，提供连贯的回答。`,
	}

	// 将系统消息放在最前面，然后是历史记录
	messages := []ai.Message{systemMessage}
	messages = append(messages, w.chatHistory...)

	return messages
}

// LoadURL 加载指定的URL
func (w *WebView) LoadURL(url string) {
	w.url = url

	// 由于Fyne没有内置的WebView支持，我们创建一个简化的界面
	// 在实际应用中，这里可以集成第三方WebView库或使用系统浏览器
	w.content = w.createWebContent()
	w.Refresh()
}

// Reload 重新加载当前页面
func (w *WebView) Reload() {
	if w.url != "" {
		w.LoadURL(w.url)
	}
}

// createWebContent 创建Web内容的替代界面
func (w *WebView) createWebContent() fyne.CanvasObject {
	// 创建一个简化的聊天界面作为WebView的替代
	return w.createSimpleChatInterface()
}

// createSimpleChatInterface 创建简化的聊天界面
func (w *WebView) createSimpleChatInterface() fyne.CanvasObject {
	// 创建聊天消息容器
	chatContainer := container.NewVBox()

	// 添加logo和欢迎消息
	// logoWidget := GetLogoWidget()
	// if logoWidget != nil {
	// 	logoContainer := container.NewCenter(logoWidget)
	// 	chatContainer.Add(logoContainer)
	// }

	welcomeMsg := w.createMessageWidget("系统", `您好！我是您的AI助手，有什么可以帮助您的吗？

🔧 功能说明：
- 支持智能模型选择
- 可复制AI回复内容
- 支持多轮对话

📁 文件处理功能：
- 支持上传PDF、Word、Excel、图片文件
- 自动解析文件内容
- 智能回答文件相关问题
- 支持文件格式转换
- 文件管理和删除

🖼️ OCR识别功能：
- 支持图片中英文文本识别
- 支持表格结构识别
- 增强PDF图片和表格识别
- 高精度文字提取

💡 使用提示：
1. 点击"上传文件"按钮选择文件
2. 等待文件解析完成
3. 询问关于文件的任何问题
4. 使用"文件管理"查看已上传的文件`, true)
	chatContainer.Add(welcomeMsg)

	// 创建滚动容器
	chatScroll := container.NewScroll(chatContainer)

	// 创建一个包装容器来强制设置滚动区域的高度
	chatWrapper := container.NewBorder(
		nil, nil, nil, nil, // 四边都为空
		chatScroll, // 中心放置滚动容器
	)
	// 设置包装容器的尺寸
	chatWrapper.Resize(fyne.NewSize(600, 400))

	// 创建一个高度控制器 - 使用不可见的占位符来控制最小高度
	// heightController := widget.NewLabel("")
	// heightController.Resize(fyne.NewSize(1, 300)) // 设置最小高度为300

	// 使用 Border 容器包装聊天滚动区域，左侧放置高度控制器
	// chatScrollWrapper := container.NewBorder(
	// 	nil, nil, // 顶部和底部为空
	// 	nil, nil, // 左侧放置高度控制器，右侧为空
	// 	chatScroll, // 中心放置聊天滚动区域
	// )

	// 输入区域
	inputEntry := widget.NewMultiLineEntry()
	inputEntry.SetPlaceHolder("请输入您的问题...")
	inputEntry.Resize(fyne.NewSize(400, 80))

	// 文件上传按钮
	uploadButton := widget.NewButton("上传文件", func() {
		w.showFileUploadDialog(chatContainer, chatScroll)
	})

	// 文件管理按钮
	manageButton := widget.NewButton("文件管理", func() {
		w.showFileManagementDialog()
	})

	// 搜索设置按钮
	searchButton := widget.NewButton("搜索设置", func() {
		w.showSearchSettingsDialog()
	})

	// 发送按钮
	sendButton := widget.NewButton("发送", func() {
		message := inputEntry.Text
		if message == "" {
			return
		}

		// 添加用户消息到历史记录
		w.addToHistory("user", message)

		// 添加用户消息到聊天容器
		userMsg := w.createMessageWidget("您", message, false)
		chatContainer.Add(userMsg)

		// 清空输入框
		inputEntry.SetText("")

		// 滚动到底部
		chatScroll.ScrollToBottom()

		// 在新的 goroutine 中获取AI回复，避免阻塞UI
		go func() {
			aiResponse := w.generateAIResponse(message)

			// 添加AI回复到历史记录
			w.addToHistory("assistant", aiResponse)

			// 在主线程中更新UI
			go func() {
				aiMsg := w.createMessageWidget("AI助手", aiResponse, false)
				chatContainer.Add(aiMsg)
				chatScroll.ScrollToBottom()
			}()
		}()
	})

	// 清空按钮
	clearButton := widget.NewButton("清空对话", func() {
		// 清空对话历史记录
		w.clearHistory()

		// 清空聊天容器
		chatContainer.Objects = nil

		// 重新添加欢迎消息
		welcomeMsg := w.createMessageWidget("系统", "对话已清空。有什么可以帮助您的吗？", true)
		chatContainer.Add(welcomeMsg)

		// 刷新容器
		chatContainer.Refresh()
	})

	// // 创建状态栏
	// statusLabel := widget.NewLabel("对话记录: 0 条")
	// statusLabel.Alignment = fyne.TextAlignCenter
	// statusLabel.Importance = widget.LowImportance

	// // 更新状态栏的函数
	// updateStatus := func() {
	// 	count := len(w.chatHistory)
	// 	maxPairs := w.maxHistorySize / 2 // 每对问答包含2条消息
	// 	statusLabel.SetText(fmt.Sprintf("对话记录: %d 条 (最多保留 %d 对问答)", count, maxPairs))
	// }

	// // 初始更新状态
	// updateStatus()

	// 输入区域布局
	buttonContainer := container.NewHBox(uploadButton, manageButton, searchButton, clearButton, sendButton)
	inputContainer := container.NewBorder(
		nil,
		buttonContainer,
		nil,
		nil,
		inputEntry,
	)

	// 整体布局
	return container.NewBorder(
		nil,
		// statusLabel,    // 顶部：状态栏
		inputContainer, // 底部：输入区域
		nil,            // 左侧
		nil,            // 右侧
		chatWrapper,    // 中心：聊天区域（使用包装器）
	)
}

// generateAIResponse 生成AI回复
func (w *WebView) generateAIResponse(message string) string {
	// 检查是否有文件相关的问题，如果有则添加文件内容到上下文
	enhancedMessage := w.enhanceMessageWithFileContext(message)

	// 检查是否启用网络搜索增强
	enhancedMessage = w.enhanceMessageWithWebSearch(enhancedMessage)

	// 从配置中获取 OpenRouter API Key
	apiKey, err := database.GetConfig("openrouter_api_key")
	if err != nil || apiKey == "" {
		logger.Log.WithFields(logrus.Fields{
			"error": err,
		}).Warn("未配置 OpenRouter API Key，使用本地回复")
		return w.getLocalResponse(enhancedMessage)
	}

	// 创建 OpenRouter 客户端
	client := ai.NewOpenRouterClient(apiKey)

	// 获取包含历史记录的完整消息列表
	messages := w.getHistoryWithSystemPrompt()

	// 添加当前用户消息（注意：这里不添加到历史记录，因为在发送按钮中已经添加了）
	currentMessage := ai.Message{
		Role:    "user",
		Content: enhancedMessage,
	}
	messages = append(messages, currentMessage)

	logger.Log.WithFields(logrus.Fields{
		"total_messages": len(messages),
		"history_count":  len(w.chatHistory),
		"current_msg":    message,
	}).Info("发送AI请求，包含历史记录")

	// 尝试多个模型进行重试
	return w.tryMultipleModels(client, messages, message)
}

// tryMultipleModels 尝试多个模型进行重试
func (w *WebView) tryMultipleModels(client *ai.OpenRouterClient, messages []ai.Message, originalMessage string) string {
	// 获取可用模型列表
	models := client.GetAvailableModels()

	// 首先尝试智能选择的模型
	selectedModel := client.SelectModel(originalMessage)
	response, err := w.tryModel(client, messages, selectedModel, originalMessage)
	if err == nil {
		return response
	}

	// 如果智能选择的模型失败，尝试其他模型
	for _, model := range models {
		if model.ID == selectedModel {
			continue // 跳过已经尝试过的模型
		}

		logger.Log.WithFields(logrus.Fields{
			"model":   model.ID,
			"message": originalMessage,
		}).Info("尝试备用模型")

		response, err := w.tryModel(client, messages, model.ID, originalMessage)
		if err == nil {
			return response
		}
	}

	// 所有模型都失败了，返回本地回复
	logger.Log.WithFields(logrus.Fields{
		"message": originalMessage,
	}).Error("所有AI模型都调用失败，使用本地回复")

	return w.getLocalResponse(originalMessage) + "\n\n*注意：所有AI服务都暂时不可用，已切换到本地回复*"
}

// tryModel 尝试单个模型
func (w *WebView) tryModel(client *ai.OpenRouterClient, messages []ai.Message, modelID string, originalMessage string) (string, error) {
	response, err := client.Chat(messages, modelID)
	if err != nil {
		logger.Log.WithFields(logrus.Fields{
			"error":   err,
			"model":   modelID,
			"message": originalMessage,
		}).Error("模型调用失败")
		return "", err
	}

	// 检查响应
	if len(response.Choices) == 0 {
		logger.Log.WithFields(logrus.Fields{
			"model":   modelID,
			"message": originalMessage,
		}).Warn("模型返回空响应")
		return "", fmt.Errorf("模型返回空响应")
	}

	// 记录使用的模型
	modelName := client.GetModelName(response.Model)
	logger.Log.WithFields(logrus.Fields{
		"model":      response.Model,
		"model_name": modelName,
		"message":    originalMessage,
		"tokens":     response.Usage.TotalTokens,
	}).Info("AI 对话成功")

	// 在回复中添加模型信息
	aiResponse := response.Choices[0].Message.Content
	aiResponse += "\n\n---\n*由 " + modelName + " 提供支持*"

	return aiResponse, nil
}

// createMessageWidget 创建可复制的消息组件
func (w *WebView) createMessageWidget(sender, content string, isSystem bool) fyne.CanvasObject {
	// 创建发送者标签和复制按钮的容器
	var headerContainer *fyne.Container

	// 创建发送者标签
	senderLabel := widget.NewLabelWithStyle(sender+":", fyne.TextAlignLeading, fyne.TextStyle{Bold: true})

	// 根据发送者设置不同的样式
	if isSystem {
		senderLabel.Importance = widget.MediumImportance
	} else if sender == "您" {
		senderLabel.Importance = widget.HighImportance
	} else {
		senderLabel.Importance = widget.LowImportance
	}

	// 创建复制按钮
	copyButton := widget.NewButton("复制", func() {
		w.copyToClipboard(content)
	})
	copyButton.Resize(fyne.NewSize(60, 30))
	copyButton.Importance = widget.LowImportance

	// 创建头部容器（发送者标签 + 复制按钮）
	headerContainer = container.NewBorder(
		nil, nil, // 上下
		nil, copyButton, // 左右：复制按钮在最右边
		senderLabel, // 中心：发送者标签
	)

	var contentWidget fyne.CanvasObject

	// 如果是AI助手的回复，尝试解析Markdown
	if sender == "AI助手" && !isSystem {
		// 使用 RichText 组件解析 Markdown
		richText := widget.NewRichTextFromMarkdown(content)
		richText.Wrapping = fyne.TextWrapWord

		// 计算合适的高度
		height := w.calculateRichTextHeight(content)
		richText.Resize(fyne.NewSize(600, height))

		contentWidget = richText
	} else {
		// 对于用户消息和系统消息，使用可复制的文本框
		// contentEntry := widget.NewMultiLineEntry()
		// contentEntry.SetText(content)
		// contentEntry.Disable() // 禁用编辑但允许选择和复制
		// contentEntry.Wrapping = fyne.TextWrapWord

		// height := w.calculateRichTextHeight(content)
		// contentEntry.Resize(fyne.NewSize(600, height))
		// // 根据内容自动计算高度
		// // w.adjustEntryHeight(contentEntry, content)

		// contentWidget = contentEntry

		richText := widget.NewRichTextFromMarkdown(content)
		richText.Wrapping = fyne.TextWrapWord

		// 计算合适的高度
		height := w.calculateRichTextHeight(content)
		richText.Resize(fyne.NewSize(600, height))

		contentWidget = richText
	}

	// 创建消息容器
	messageContainer := container.NewVBox(
		headerContainer,
		contentWidget,
		widget.NewSeparator(),
	)

	return messageContainer
}

// copyToClipboard 复制内容到剪贴板
func (w *WebView) copyToClipboard(content string) {
	// 获取应用的剪贴板
	clipboard := fyne.CurrentApp().Clipboard()
	clipboard.SetContent(content)

	// 记录日志
	logger.Log.WithFields(logrus.Fields{
		"content_length": len(content),
	}).Info("内容已复制到剪贴板")

	// 如果有窗口引用，显示通知
	if w.window != nil {
		// 创建一个简单的通知对话框
		info := widget.NewLabel("✓ 内容已复制到剪贴板")
		info.Alignment = fyne.TextAlignCenter

		dialog := container.NewCenter(info)
		popup := widget.NewModalPopUp(dialog, w.window.Canvas())
		popup.Resize(fyne.NewSize(200, 60))
		popup.Show()

		// 1.5秒后自动关闭
		go func() {
			time.Sleep(1500 * time.Millisecond)
			popup.Hide()
		}()
	}
}

// calculateRichTextHeight 计算 RichText 组件的合适高度
func (w *WebView) calculateRichTextHeight(content string) float32 {
	// 计算文本行数
	lines := w.countLines(content)

	// 设置最小和最大行数
	minLines := 3  // 最少显示3行
	maxLines := 30 // 最大显示30行

	// 限制行数范围
	if lines < minLines {
		lines = minLines
	}
	if lines > maxLines {
		lines = maxLines
	}

	// RichText 每行大约25像素高度，加上一些边距
	lineHeight := 25.0
	padding := 20.0
	height := float32(float64(lines)*lineHeight + padding)

	// 设置最小高度
	minHeight := float32(80)
	if height < minHeight {
		height = minHeight
	}

	return height
}

// adjustEntryHeight 根据内容自动调整文本框高度
func (w *WebView) adjustEntryHeight(entry *widget.Entry, content string) {
	// 计算文本行数
	lines := w.countLines(content)

	// 设置最小和最大行数
	minLines := 2  // 最少显示2行
	maxLines := 20 // 最大显示20行，超过的会有滚动条

	// 限制行数范围
	if lines < minLines {
		lines = minLines
	}
	if lines > maxLines {
		lines = maxLines
	}

	// 每行大约20像素高度，加上一些边距
	lineHeight := 20.0
	padding := 20.0
	height := float32(float64(lines)*lineHeight + padding)

	// 设置最小高度
	minHeight := float32(60)
	if height < minHeight {
		height = minHeight
	}

	// 设置固定宽度，自适应高度
	width := float32(600)

	// 使用 MinSize 而不是 Resize 来确保高度自适应
	entry.Resize(fyne.NewSize(width, height))

	// 强制刷新组件
	entry.Refresh()
}

// countLines 计算文本的行数
func (w *WebView) countLines(content string) int {
	if content == "" {
		return 1
	}

	// 按换行符分割文本
	textLines := strings.Split(content, "\n")
	totalLines := 0

	// 每行可显示的字符数（考虑中英文混合）
	charsPerLine := 70 // 增加每行字符数以适应更宽的显示区域

	for _, line := range textLines {
		if line == "" {
			totalLines++ // 空行也占一行
			continue
		}

		// 计算这一行需要多少显示行
		runes := []rune(line)
		lineLength := len(runes)

		// 计算自动换行的行数
		wrappedLines := (lineLength + charsPerLine - 1) / charsPerLine
		if wrappedLines == 0 {
			wrappedLines = 1
		}

		totalLines += wrappedLines
	}

	return totalLines
}

// getLocalResponse 获取本地预设回复
func (w *WebView) getLocalResponse(message string) string {
	responses := map[string]string{
		"你好": "您好！很高兴为您服务。有什么我可以帮助您的吗？",
		"您好": "您好！很高兴为您服务。有什么我可以帮助您的吗？",
		"谢谢": "不客气！如果您还有其他问题，随时可以问我。",
		"感谢": "不客气！如果您还有其他问题，随时可以问我。",
		"再见": "再见！祝您工作顺利，有需要随时联系我。",
		"拜拜": "再见！祝您工作顺利，有需要随时联系我。",
		"帮助": `我可以帮助您解答各种问题，包括：
- 系统使用指导
- 数据上报相关问题
- 文件上传和解析
- 文档内容分析
- 文件格式转换
- 技术支持`,
		"功能": `本系统主要功能包括：
- 数据上报
- 历史记录查看
- 模板下载
- 系统管理
- AI对话助手
- 文件上传解析
- 文档内容分析
- 文件格式转换`,
		"配置": "要使用AI对话功能，请在配置管理中添加 'openrouter_api_key' 配置项，并设置您的 OpenRouter API 密钥。",
		"文件": `文件处理功能说明：
1. 支持的文件类型：PDF、Word(.docx)、Excel(.xlsx/.xls)
2. 点击"上传文件"按钮选择文件
3. 系统会自动解析文件内容
4. 您可以询问关于文件的任何问题
5. 支持文件格式转换
6. 使用"文件管理"查看和管理已上传的文件`,
		"上传": "请点击界面下方的\"上传文件\"按钮来上传您的文件。支持PDF、Word、Excel格式。",
		"转换": "文件上传并解析完成后，您可以在\"文件管理\"中选择文件进行格式转换。",
	}

	// 检查是否有预设回复
	for key, response := range responses {
		if message == key {
			return response
		}
	}

	// 默认回复
	return "我理解您的问题。基于您的输入「" + message + "」，我建议您可以：\n\n1. 查看相关的系统功能模块\n2. 参考帮助文档\n3. 如需更详细的帮助，请提供更多具体信息\n\n**提示：** 要启用完整的AI对话功能，请在配置管理中设置 'openrouter_api_key'。"
}

// enhanceMessageWithWebSearch 使用网络搜索增强消息
func (w *WebView) enhanceMessageWithWebSearch(message string) string {
	// 检查搜索增强器是否可用且启用
	if w.searchEnhancer == nil || !w.searchEnhancer.IsEnabled() {
		return message
	}

	// 使用搜索增强器增强消息
	enhancedMessage := w.searchEnhancer.EnhanceMessage(message)

	logger.Log.WithFields(logrus.Fields{
		"original_length": len(message),
		"enhanced_length": len(enhancedMessage),
		"search_enabled":  true,
	}).Info("网络搜索增强完成")

	return enhancedMessage
}

// CreateRenderer 实现fyne.Widget接口
func (w *WebView) CreateRenderer() fyne.WidgetRenderer {
	return &webViewRenderer{
		webView: w,
		content: w.content,
	}
}

// webViewRenderer WebView的渲染器
type webViewRenderer struct {
	webView *WebView
	content fyne.CanvasObject
}

func (r *webViewRenderer) Layout(size fyne.Size) {
	if r.content != nil {
		r.content.Resize(size)
	}
}

func (r *webViewRenderer) MinSize() fyne.Size {
	if r.content != nil {
		return r.content.MinSize()
	}
	return fyne.NewSize(400, 300)
}

func (r *webViewRenderer) Refresh() {
	r.content = r.webView.content
	if r.content != nil {
		r.content.Refresh()
	}
}

func (r *webViewRenderer) Objects() []fyne.CanvasObject {
	if r.content != nil {
		return []fyne.CanvasObject{r.content}
	}
	return []fyne.CanvasObject{}
}

func (r *webViewRenderer) Destroy() {
	// 清理资源
}

// showFileUploadDialog 显示文件上传对话框
func (w *WebView) showFileUploadDialog(chatContainer *fyne.Container, chatScroll *container.Scroll) {
	if w.window == nil {
		return
	}

	// 创建文件选择对话框
	dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			w.showErrorMessage("文件选择失败: " + err.Error())
			return
		}
		if reader == nil {
			return
		}
		defer reader.Close()

		filePath := reader.URI().Path()
		fileName := reader.URI().Name()

		// 验证文件类型
		err = fileparser.ValidateFile(filePath)
		if err != nil {
			w.showErrorMessage("文件验证失败: " + err.Error())
			return
		}

		// 显示上传进度
		progressDialog := dialog.NewInformation("上传中", "正在上传文件: "+fileName, w.window)
		progressDialog.Show()

		// 异步上传文件
		go func() {
			fileInfo, err := w.fileManager.UploadFile(filePath, fileName)

			// 关闭进度对话框
			progressDialog.Hide()

			if err != nil {
				w.showErrorMessage("文件上传失败: " + err.Error())
				return
			}

			// 在聊天界面显示上传成功消息
			message := fmt.Sprintf("✅ 文件上传成功！\n文件名: %s\n文件类型: %s\n文件大小: %.2f KB\n\n您现在可以询问关于这个文件的问题了。",
				fileInfo.OriginalName,
				fileparser.GetFileTypeString(fileInfo.FileType),
				float64(fileInfo.Size)/1024)

			systemMsg := w.createMessageWidget("系统", message, true)
			chatContainer.Add(systemMsg)
			chatScroll.ScrollToBottom()
		}()

	}, w.window)
}

// showFileManagementDialog 显示文件管理对话框
func (w *WebView) showFileManagementDialog() {
	if w.window == nil {
		return
	}

	// 获取文件列表
	files := w.fileManager.ListFiles()

	// 创建文件列表
	var fileItems []string
	fileMap := make(map[string]*fileparser.FileInfo)

	for _, file := range files {
		displayName := fmt.Sprintf("%s (%s) - %s",
			file.OriginalName,
			fileparser.GetFileTypeString(file.FileType),
			file.Status)
		fileItems = append(fileItems, displayName)
		fileMap[displayName] = file
	}

	if len(fileItems) == 0 {
		dialog.ShowInformation("文件管理", "暂无已上传的文件", w.window)
		return
	}

	// 创建文件选择列表
	var selectedIndex int = -1
	fileList := widget.NewList(
		func() int { return len(fileItems) },
		func() fyne.CanvasObject {
			return widget.NewLabel("文件项")
		},
		func(id widget.ListItemID, obj fyne.CanvasObject) {
			obj.(*widget.Label).SetText(fileItems[id])
		},
	)

	// 设置选择回调
	fileList.OnSelected = func(id widget.ListItemID) {
		selectedIndex = id
	}

	// 创建操作按钮
	deleteButton := widget.NewButton("删除文件", func() {
		if selectedIndex < 0 || selectedIndex >= len(fileItems) {
			dialog.ShowInformation("提示", "请先选择要删除的文件", w.window)
			return
		}

		selectedItem := fileItems[selectedIndex]
		fileInfo := fileMap[selectedItem]

		dialog.ShowConfirm("确认删除",
			fmt.Sprintf("确定要删除文件 '%s' 吗？", fileInfo.OriginalName),
			func(confirmed bool) {
				if confirmed {
					err := w.fileManager.DeleteFile(fileInfo.ID)
					if err != nil {
						w.showErrorMessage("删除失败: " + err.Error())
					} else {
						dialog.ShowInformation("成功", "文件已删除", w.window)
					}
				}
			}, w.window)
	})

	convertButton := widget.NewButton("转换文件", func() {
		if selectedIndex < 0 || selectedIndex >= len(fileItems) {
			dialog.ShowInformation("提示", "请先选择要转换的文件", w.window)
			return
		}

		selectedItem := fileItems[selectedIndex]
		fileInfo := fileMap[selectedItem]

		w.showFileConvertDialog(fileInfo)
	})

	// 创建对话框内容
	content := container.NewBorder(
		widget.NewLabel("已上传的文件:"),
		container.NewHBox(deleteButton, convertButton),
		nil, nil,
		fileList,
	)

	// 显示对话框
	go func() {
		customDialog := dialog.NewCustom("文件管理", "关闭", content, w.window)
		customDialog.Resize(fyne.NewSize(400, 200))
		customDialog.Show()
	}()
}

// showFileConvertDialog 显示文件转换对话框
func (w *WebView) showFileConvertDialog(fileInfo *fileparser.FileInfo) {
	if fileInfo.Status != "parsed" {
		dialog.ShowInformation("提示", "文件尚未解析完成，无法转换", w.window)
		return
	}

	// 获取支持的转换格式
	converter := fileparser.NewFileConverter()
	supportedConversions := converter.GetSupportedConversions()

	fileTypeStr := fileparser.GetFileTypeString(fileInfo.FileType)
	formats, exists := supportedConversions[fileTypeStr]
	if !exists || len(formats) == 0 {
		dialog.ShowInformation("提示", "该文件类型不支持转换", w.window)
		return
	}

	// 创建格式选择
	formatSelect := widget.NewSelect(formats, nil)
	formatSelect.SetSelected(formats[0])

	// 创建转换按钮
	convertButton := widget.NewButton("开始转换", func() {
		selectedFormat := formatSelect.Selected
		if selectedFormat == "" {
			dialog.ShowInformation("提示", "请选择转换格式", w.window)
			return
		}

		// 显示转换进度
		progressDialog := dialog.NewInformation("转换中", "正在转换文件...", w.window)
		progressDialog.Show()

		// 异步转换
		go func() {
			result, err := w.fileManager.ConvertFile(fileInfo.ID, selectedFormat)
			progressDialog.Hide()

			if err != nil {
				w.showErrorMessage("转换失败: " + err.Error())
				return
			}

			if result.Success {
				dialog.ShowInformation("转换成功",
					fmt.Sprintf("文件已转换完成！\n输出路径: %s", result.OutputPath),
					w.window)
			} else {
				w.showErrorMessage("转换失败: " + result.Error.Error())
			}
		}()
	})

	// 创建对话框内容
	content := container.NewVBox(
		widget.NewLabel(fmt.Sprintf("转换文件: %s", fileInfo.OriginalName)),
		widget.NewLabel("选择目标格式:"),
		formatSelect,
		convertButton,
	)

	dialog.ShowCustom("文件转换", "取消", content, w.window)
}

// showErrorMessage 显示错误消息
func (w *WebView) showErrorMessage(message string) {
	if w.window != nil {
		dialog.ShowError(fmt.Errorf("%s", message), w.window)
	}
}

// enhanceMessageWithFileContext 增强消息，添加文件上下文
func (w *WebView) enhanceMessageWithFileContext(message string) string {
	// 检查消息中是否包含文件相关的关键词
	fileKeywords := []string{
		"文件", "文档", "内容", "总结", "分析", "解析", "转换",
		"PDF", "Word", "Excel", "图片", "图像", "照片", "上传", "这个文件", "刚才的文件",
		"OCR", "识别", "文字识别", "表格", "扫描", "提取文字", "文本识别",
	}

	hasFileKeyword := false
	lowerMessage := strings.ToLower(message)
	for _, keyword := range fileKeywords {
		if strings.Contains(lowerMessage, strings.ToLower(keyword)) {
			hasFileKeyword = true
			break
		}
	}

	// 如果没有文件相关关键词，直接返回原消息
	if !hasFileKeyword {
		return message
	}

	// 获取最近上传的文件
	files := w.fileManager.ListFiles()
	if len(files) == 0 {
		return message + "\n\n[注意：您提到了文件，但当前没有已上传的文件。请先上传文件。]"
	}

	// 找到最近上传且已解析的文件
	var recentFile *fileparser.FileInfo
	for i := len(files) - 1; i >= 0; i-- {
		if files[i].Status == "parsed" {
			recentFile = files[i]
			break
		}
	}

	if recentFile == nil {
		return message + "\n\n[注意：您提到了文件，但当前没有已解析完成的文件。请等待文件解析完成或重新上传。]"
	}

	// 获取文件内容
	content, err := w.fileManager.GetFileContent(recentFile.ID)
	if err != nil {
		return message + fmt.Sprintf("\n\n[注意：无法获取文件内容: %v]", err)
	}

	// 限制文件内容长度，避免消息过长
	maxContentLength := 2000
	if len(content) > maxContentLength {
		content = content[:maxContentLength] + "...(内容已截断)"
	}

	// 构建增强的消息
	enhancedMessage := fmt.Sprintf(`%s

[文件上下文]
文件名: %s
文件类型: %s
文件大小: %.2f KB
上传时间: %s

文件内容:
%s

请基于以上文件内容回答我的问题。`,
		message,
		recentFile.OriginalName,
		fileparser.GetFileTypeString(recentFile.FileType),
		float64(recentFile.Size)/1024,
		recentFile.UploadTime.Format("2006-01-02 15:04:05"),
		content)

	return enhancedMessage
}

// showSearchSettingsDialog 显示搜索设置对话框
func (w *WebView) showSearchSettingsDialog() {
	// 获取当前配置
	enableSearch, _ := database.GetConfig("enable_web_search")
	mcpURL, _ := database.GetConfig("mcp_websearch_url")

	if mcpURL == "" {
		mcpURL = "http://localhost:30000/mcp"
	}

	// 创建启用搜索的复选框
	enableCheckbox := widget.NewCheck("启用网络搜索增强", nil)
	enableCheckbox.SetChecked(enableSearch == "true")

	// 创建MCP服务地址输入框
	urlEntry := widget.NewEntry()
	urlEntry.SetText(mcpURL)
	urlEntry.SetPlaceHolder("MCP服务地址，如: http://localhost:3000/mcp")

	// 创建测试连接按钮
	testButton := widget.NewButton("测试连接", func() {
		testURL := urlEntry.Text
		if testURL == "" {
			dialog.ShowInformation("提示", "请输入MCP服务地址", w.window)
			return
		}

		// 测试连接
		go func() {
			// 临时更新URL进行测试
			originalURL := ""
			if w.searchEnhancer != nil {
				// 保存原始URL
				originalURL = w.searchEnhancer.GetMCPURL()
				// 临时设置测试URL
				w.searchEnhancer.UpdateMCPURL(testURL)
			}

			// 测试连接
			if w.searchEnhancer != nil && w.searchEnhancer.TestConnection() {
				dialog.ShowInformation("连接测试", "✅ MCP服务连接成功！", w.window)
			} else {
				dialog.ShowInformation("连接测试", "❌ MCP服务连接失败，请检查服务是否启动", w.window)
			}

			// 恢复原始URL（如果测试失败）
			if originalURL != "" && w.searchEnhancer != nil {
				w.searchEnhancer.UpdateMCPURL(originalURL)
			}
		}()
	})

	// 创建保存按钮
	saveButton := widget.NewButton("保存设置", func() {
		// 保存启用状态
		enableValue := "false"
		if enableCheckbox.Checked {
			enableValue = "true"
		}
		database.AddConfig("enable_web_search", enableValue)

		// 保存MCP服务地址
		database.AddConfig("mcp_websearch_url", urlEntry.Text)

		// 更新搜索增强器的配置
		if w.searchEnhancer != nil {
			w.searchEnhancer.UpdateMCPURL(urlEntry.Text)
		}

		dialog.ShowInformation("设置", "搜索设置已保存！", w.window)
	})

	// 创建说明文本
	infoText := widget.NewRichTextFromMarkdown(`
## 🔍 网络搜索增强功能

### 功能说明
- 自动检测用户问题是否需要搜索
- 支持多引擎搜索（Bing、百度、CSDN等）
- 将搜索结果整合到AI回复中
- 提供实时、准确的信息

### 使用前提
1. 需要启动open-websearch MCP服务
2. 默认服务地址：http://localhost:30000/mcp
3. 可以使用Docker快速部署：
   ` + "`docker run -d -p 30000:30000 ghcr.io/aas-ee/open-web-search:latest`" + `

### 触发条件
包含以下关键词时会自动搜索：
- 搜索、查找、最新、新闻、什么是、如何
- 教程、方法、比较、推荐、价格等
`)

	// 创建滚动容器
	scrollContainer := container.NewScroll(infoText)

	// 创建对话框内容
	content := container.NewVBox(
		enableCheckbox,
		widget.NewSeparator(),
		widget.NewLabel("MCP服务地址:"),
		urlEntry,
		container.NewHBox(testButton, saveButton),
		widget.NewSeparator(),
		// 直接使用滚动容器，在对话框显示后设置尺寸
		scrollContainer,
	)

	// 显示对话框
	d := dialog.NewCustom("搜索设置", "关闭", content, w.window)
	d.Resize(fyne.NewSize(500, 600))

	// 设置对话框关闭回调，在显示后调整滚动容器尺寸
	d.SetOnClosed(func() {
		// 对话框关闭时的处理
	})

	d.Show()

	// 对话框显示后，强制设置滚动容器的尺寸
	// 使用延迟执行确保对话框已完全显示
	go func() {
		// 等待对话框完全显示
		time.Sleep(100 * time.Millisecond)
		// 设置滚动容器的尺寸
		scrollContainer.Resize(fyne.NewSize(480, 300))
		scrollContainer.Refresh()
	}()
}
