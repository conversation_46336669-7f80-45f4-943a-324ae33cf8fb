package ui

import (
	"log"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/widget"
)

// 资源管理
var (
	// Logo图片资源
	LogoResource fyne.Resource
	// 应用图标资源
	IconResource fyne.Resource
)

// InitResources 初始化资源
func InitResources() {
	// 尝试加载logo图片
	loadLogoResource()

	// 尝试加载应用图标
	loadIconResource()

	// 确保资源初始化完成
	if LogoResource == nil {
		log.Println("Logo资源未加载，将使用默认文本标签")
	}

	if IconResource == nil {
		log.Println("图标资源未加载，将使用默认图标")
	}
}

// loadLogoResource 加载logo资源
func loadLogoResource() {
	// 尝试多个可能的路径
	logoPaths := []string{
		"logo/logo.png",
	}

	for _, path := range logoPaths {
		if resource := tryLoadImageResource(path); resource != nil {
			LogoResource = resource
			return
		}
	}
}

// loadIconResource 加载图标资源
func loadIconResource() {
	// 尝试多个可能的路径
	iconPaths := []string{
		"logo/logo_16x16.ico",
		"logo/logo_24x24.ico",
	}

	for _, path := range iconPaths {
		if resource := tryLoadImageResource(path); resource != nil {
			IconResource = resource
			return
		}
	}
}

// tryLoadImageResource 尝试加载图片资源
func tryLoadImageResource(path string) fyne.Resource {
	// 这里可以添加实际的图片加载逻辑
	// 由于Fyne的资源加载API比较复杂，暂时返回nil
	// 在实际部署时可以使用fyne bundle工具预生成资源
	return nil
}

// GetLogoWidget 获取logo组件
func GetLogoWidget() fyne.CanvasObject {
	if LogoResource != nil {
		// 如果有logo资源，创建图片组件
		logoImage := widget.NewIcon(LogoResource)
		logoImage.Resize(fyne.NewSize(64, 64))
		return container.NewCenter(logoImage)
	}

	// 如果没有logo资源，返回文本标签作为fallback
	logoLabel := widget.NewLabel("📊 库存管理系统")
	logoLabel.Alignment = fyne.TextAlignCenter
	logoLabel.TextStyle = fyne.TextStyle{Bold: true}

	// 创建一个容器来包装logo，设置合适的大小
	logoContainer := container.NewCenter(logoLabel)
	logoContainer.Resize(fyne.NewSize(200, 64))

	return logoContainer
}

// SetAppIcon 设置应用程序图标
func SetAppIcon(app fyne.App) {
	// 暂时不设置图标，在实际部署时可以添加
	// if IconResource != nil {
	//     app.SetIcon(IconResource)
	// }
}
