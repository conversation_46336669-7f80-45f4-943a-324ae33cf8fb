# OCR功能演示指南

## 快速体验OCR功能

### 1. 启动应用程序
```bash
cd src
go run ./cmd
```

### 2. 登录系统
- 使用管理员账户登录
- 切换到"AI助手"标签页

### 3. 上传图片文件
1. 点击"上传文件"按钮
2. 选择一张包含文字的图片（JPG、PNG、BMP等）
3. 等待文件上传和处理完成

### 4. 与AI对话测试OCR
上传图片后，可以尝试以下对话：

**文本识别测试：**
- "这张图片里有什么文字？"
- "帮我提取图片中的所有文本"
- "图片上写的是什么内容？"

**表格识别测试：**
- "这张图片中有表格吗？"
- "帮我识别表格内容"
- "表格有多少行多少列？"

**内容分析测试：**
- "总结一下图片的内容"
- "这是什么类型的文档？"
- "帮我分析图片中的信息"

### 5. PDF增强处理测试
1. 上传一个包含图片或表格的PDF文件
2. 询问："这个PDF文件包含哪些内容？"
3. 系统会自动进行增强处理，提取文本、图片和表格

## 功能展示要点

### 🎯 核心功能
- ✅ 支持多种图片格式（JPG、PNG、BMP、TIFF、GIF）
- ✅ 中英文文本识别
- ✅ 表格结构识别
- ✅ PDF增强处理
- ✅ 图片预处理优化
- ✅ 智能文件类型检测

### 🔧 技术特性
- ✅ 模块化OCR服务架构
- ✅ 异步文件处理
- ✅ 错误处理和日志记录
- ✅ 用户友好的界面集成
- ✅ 文件管理和清理

### 📊 处理流程
1. **文件上传** → 验证文件类型
2. **图片预处理** → 灰度化、对比度调整、锐化
3. **OCR识别** → 文本提取、置信度评估
4. **表格识别** → 结构分析、单元格提取
5. **结果整合** → 格式化输出、上下文增强

## 当前实现说明

### 模拟OCR服务
当前版本使用模拟的OCR服务，主要用于：
- 验证系统架构的正确性
- 测试文件处理流程
- 演示用户界面集成
- 提供开发和测试环境

### 模拟结果示例
上传图片后，系统会返回类似以下的模拟识别结果：

```
这是一张包含中英文文本的图片。
This is an image containing Chinese and English text.

示例内容：
- 产品名称：智能手机
- 价格：¥2999
- 库存：100台
```

### 表格识别示例
```
表格识别结果 (4行 x 3列):
姓名 | 年龄 | 职位
张三 | 28 | 工程师
李四 | 32 | 经理
王五 | 25 | 设计师
```

## 生产环境部署

### 安装真实OCR引擎
要在生产环境中使用真实的OCR功能，需要：

1. **安装Tesseract OCR**
   ```bash
   # Windows
   下载：https://github.com/UB-Mannheim/tesseract/wiki
   
   # Linux
   sudo apt-get install tesseract-ocr
   sudo apt-get install tesseract-ocr-chi-sim
   sudo apt-get install tesseract-ocr-chi-tra
   ```

2. **安装Go依赖**
   ```bash
   go get github.com/otiai10/gosseract/v2
   ```

3. **替换模拟服务**
   修改`src/ocr/service.go`，使用真实的Tesseract调用

### 性能优化建议
- 使用图片压缩减少处理时间
- 实现并发处理提高吞吐量
- 添加缓存机制避免重复处理
- 监控内存使用防止溢出

## 测试用例

### 推荐测试图片类型
1. **纯文本图片**：扫描的文档、截图等
2. **表格图片**：Excel截图、报表图片等
3. **混合内容**：包含文字和图表的复合图片
4. **多语言文本**：中英文混合的文档
5. **手写文字**：清晰的手写笔记（识别率可能较低）

### 测试PDF文件
1. **纯文本PDF**：测试基本文本提取
2. **图片PDF**：扫描版PDF，测试OCR功能
3. **表格PDF**：包含表格的报告文档
4. **混合PDF**：文本、图片、表格混合的复杂文档

## 用户反馈收集

在测试过程中，请关注：
- 识别准确率如何？
- 处理速度是否满意？
- 界面操作是否直观？
- 错误提示是否清晰？
- 还需要哪些功能？

## 技术支持

如遇到问题，请检查：
1. 应用程序日志文件
2. 文件格式是否支持
3. 图片质量是否清晰
4. 系统资源是否充足

联系开发团队获取技术支持和功能改进建议。
