chcp 65001
@echo off
echo 启动open-websearch MCP服务...
echo.

REM 检查Docker是否安装
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Docker，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo Docker已检测到，正在启动服务...
echo.

REM 启动服务
docker-compose -f docker-compose-websearch.yml up -d

if %errorlevel% equ 0 (
    echo.
    echo ✅ open-websearch MCP服务启动成功！
    echo.
    echo 服务信息:
    echo - 服务地址: http://localhost:3000/mcp
    echo - 健康检查: http://localhost:3000/health
    echo - 容器名称: open-websearch-mcp
    echo.
    echo 现在可以在AI助手中启用网络搜索功能了！
    echo 请在AI助手的"搜索设置"中启用网络搜索增强。
    echo.
    echo 查看服务状态: docker-compose -f docker-compose-websearch.yml ps
    echo 查看服务日志: docker-compose -f docker-compose-websearch.yml logs -f
    echo 停止服务: docker-compose -f docker-compose-websearch.yml down
) else (
    echo.
    echo ❌ 服务启动失败，请检查错误信息
)

echo.
pause
