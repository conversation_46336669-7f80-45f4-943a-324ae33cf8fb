package ui

import (
	"encoding/csv"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"simple_inventory_management_system/internal/database"
	"simple_inventory_management_system/internal/logger"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/container"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/widget"
	"github.com/sirupsen/logrus"
	"github.com/xuri/excelize/v2"
)

// createDatabaseManagementTab 创建数据库管理标签页
func (a *App) createDatabaseManagementTab() fyne.CanvasObject {
	// 创建主容器
	mainContainer := container.NewVBox()

	// 1. 表数据导出功能
	exportSection := a.createExportSection()

	// 2. 表数据导入功能
	importSection := a.createImportSection()

	// 3. SQL 执行功能
	sqlSection := a.createSQLSection()

	// 添加所有部分到主容器
	mainContainer.Add(widget.NewCard("数据导出", "", exportSection))
	mainContainer.Add(widget.NewCard("数据导入", "", importSection))
	mainContainer.Add(widget.NewCard("SQL 执行", "", sqlSection))

	// 创建滚动容器
	scroll := container.NewScroll(mainContainer)
	scroll.SetMinSize(fyne.NewSize(800, 600))

	return scroll
}

// createExportSection 创建数据导出部分
func (a *App) createExportSection() fyne.CanvasObject {
	// 表名选择
	tableSelect := widget.NewSelect([]string{
		"admin_divisions",
		"categories",
		"configs",
		"regions",
		"submissions",
		"users",
	}, nil)
	tableSelect.SetSelected("admin_divisions")

	// 导出格式选择
	formatSelect := widget.NewSelect([]string{"Excel", "CSV"}, nil)
	formatSelect.SetSelected("Excel")

	// 结果显示
	resultLabel := widget.NewLabel("请选择要导出的表和格式")
	resultLabel.Wrapping = fyne.TextWrapWord

	// 导出按钮
	exportBtn := widget.NewButton("导出数据", func() {
		if tableSelect.Selected == "" {
			dialog.ShowError(fmt.Errorf("请选择要导出的表"), a.mainWin)
			return
		}

		resultLabel.SetText("正在导出数据...")

		go func() {
			filePath, err := a.exportTableData(tableSelect.Selected, formatSelect.Selected)
			if err != nil {
				resultLabel.SetText(fmt.Sprintf("导出失败: %v", err))
				logger.Log.WithFields(logrus.Fields{
					"table":  tableSelect.Selected,
					"format": formatSelect.Selected,
					"error":  err,
				}).Error("数据导出失败")
			} else {
				resultLabel.SetText(fmt.Sprintf("导出成功！文件保存到: %s", filePath))
				logger.Log.WithFields(logrus.Fields{
					"table":    tableSelect.Selected,
					"format":   formatSelect.Selected,
					"filePath": filePath,
				}).Info("数据导出成功")
			}
		}()
	})

	// 布局
	form := widget.NewForm(
		widget.NewFormItem("选择表", tableSelect),
		widget.NewFormItem("导出格式", formatSelect),
	)

	return container.NewVBox(
		form,
		exportBtn,
		widget.NewSeparator(),
		resultLabel,
	)
}

// createImportSection 创建数据导入部分
func (a *App) createImportSection() fyne.CanvasObject {
	// 表名选择
	tableSelect := widget.NewSelect([]string{
		"admin_divisions",
		"categories",
		"regions",
	}, nil)
	tableSelect.SetSelected("admin_divisions")

	// 文件路径输入
	filePathEntry := widget.NewEntry()
	filePathEntry.SetPlaceHolder("选择要导入的文件...")

	// 文件选择按钮
	fileSelectBtn := widget.NewButton("选择文件", func() {
		dialog.ShowFileOpen(func(reader fyne.URIReadCloser, err error) {
			if err != nil {
				dialog.ShowError(err, a.mainWin)
				return
			}
			if reader == nil {
				return
			}
			defer reader.Close()

			filePathEntry.SetText(reader.URI().Path())
		}, a.mainWin)
	})

	// 结果显示
	resultLabel := widget.NewLabel("请选择表和文件进行导入")
	resultLabel.Wrapping = fyne.TextWrapWord

	// 导入按钮
	importBtn := widget.NewButton("导入数据", func() {
		if tableSelect.Selected == "" {
			dialog.ShowError(fmt.Errorf("请选择要导入的表"), a.mainWin)
			return
		}
		if filePathEntry.Text == "" {
			dialog.ShowError(fmt.Errorf("请选择要导入的文件"), a.mainWin)
			return
		}

		resultLabel.SetText("正在导入数据...")

		go func() {
			count, err := a.importTableData(tableSelect.Selected, filePathEntry.Text)
			if err != nil {
				resultLabel.SetText(fmt.Sprintf("导入失败: %v", err))
				logger.Log.WithFields(logrus.Fields{
					"table":    tableSelect.Selected,
					"filePath": filePathEntry.Text,
					"error":    err,
				}).Error("数据导入失败")
			} else {
				resultLabel.SetText(fmt.Sprintf("导入成功！共导入 %d 条记录", count))
				logger.Log.WithFields(logrus.Fields{
					"table":    tableSelect.Selected,
					"filePath": filePathEntry.Text,
					"count":    count,
				}).Info("数据导入成功")
			}
		}()
	})

	// 布局
	form := widget.NewForm(
		widget.NewFormItem("选择表", tableSelect),
		widget.NewFormItem("选择文件", container.NewBorder(nil, nil, nil, fileSelectBtn, filePathEntry)),
	)

	return container.NewVBox(
		form,
		importBtn,
		widget.NewSeparator(),
		resultLabel,
	)
}

// createSQLSection 创建 SQL 执行部分
func (a *App) createSQLSection() fyne.CanvasObject {
	// SQL 输入框
	sqlEntry := widget.NewMultiLineEntry()
	sqlEntry.SetPlaceHolder("请输入 SQL 语句...")
	sqlEntry.Resize(fyne.NewSize(600, 150))

	// 结果显示
	resultEntry := widget.NewMultiLineEntry()
	resultEntry.SetPlaceHolder("执行结果将显示在这里...")
	resultEntry.Resize(fyne.NewSize(600, 200))

	// 执行按钮
	executeBtn := widget.NewButton("执行 SQL", func() {
		sqlText := strings.TrimSpace(sqlEntry.Text)
		if sqlText == "" {
			dialog.ShowError(fmt.Errorf("请输入 SQL 语句"), a.mainWin)
			return
		}

		resultEntry.SetText("正在执行 SQL...")

		go func() {
			result, err := a.executeSQL(sqlText)
			if err != nil {
				resultEntry.SetText(fmt.Sprintf("执行失败: %v", err))
				logger.Log.WithFields(logrus.Fields{
					"sql":   sqlText,
					"error": err,
				}).Error("SQL执行失败")
			} else {
				resultEntry.SetText(result)
				logger.Log.WithFields(logrus.Fields{
					"sql": sqlText,
				}).Info("SQL执行成功")
			}
		}()
	})

	// 清空按钮
	clearBtn := widget.NewButton("清空", func() {
		sqlEntry.SetText("")
		resultEntry.SetText("")
	})

	// 常用 SQL 模板按钮
	templatesContainer := container.NewHBox(
		widget.NewButton("查看表结构", func() {
			sqlEntry.SetText("PRAGMA table_info(admin_divisions);")
		}),
		widget.NewButton("查看所有表", func() {
			sqlEntry.SetText("SELECT name FROM sqlite_master WHERE type='table';")
		}),
		widget.NewButton("统计记录数", func() {
			sqlEntry.SetText("SELECT COUNT(*) as count FROM admin_divisions WHERE is_deleted = 0;")
		}),
	)

	// 布局
	return container.NewVBox(
		widget.NewLabel("SQL 语句:"),
		sqlEntry,
		container.NewHBox(executeBtn, clearBtn),
		widget.NewLabel("常用模板:"),
		templatesContainer,
		widget.NewSeparator(),
		widget.NewLabel("执行结果:"),
		resultEntry,
	)
}

// exportTableData 导出表数据
func (a *App) exportTableData(tableName, format string) (string, error) {
	// 查询数据
	rows, err := database.DB.Raw(fmt.Sprintf("SELECT * FROM %s", tableName)).Rows()
	if err != nil {
		return "", fmt.Errorf("查询数据失败: %v", err)
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return "", fmt.Errorf("获取列名失败: %v", err)
	}

	// 读取所有数据
	var data [][]string
	data = append(data, columns) // 添加表头

	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return "", fmt.Errorf("扫描数据失败: %v", err)
		}

		row := make([]string, len(columns))
		for i, val := range values {
			if val != nil {
				row[i] = fmt.Sprintf("%v", val)
			} else {
				row[i] = ""
			}
		}
		data = append(data, row)
	}

	// 获取下载目录
	downloadDir, err := GetDownloadDir()
	if err != nil {
		downloadDir = os.TempDir()
	}

	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	var filePath string

	if format == "Excel" {
		fileName := fmt.Sprintf("%s_export_%s.xlsx", tableName, timestamp)
		filePath = filepath.Join(downloadDir, fileName)
		err = a.exportToExcel(data, filePath)
	} else {
		fileName := fmt.Sprintf("%s_export_%s.csv", tableName, timestamp)
		filePath = filepath.Join(downloadDir, fileName)
		err = a.exportToCSV(data, filePath)
	}

	if err != nil {
		return "", fmt.Errorf("保存文件失败: %v", err)
	}

	return filePath, nil
}

// exportToExcel 导出到 Excel 文件
func (a *App) exportToExcel(data [][]string, filePath string) error {
	f := excelize.NewFile()
	defer f.Close()

	sheetName := "Sheet1"

	// 写入数据
	for i, row := range data {
		for j, cell := range row {
			cellName, _ := excelize.CoordinatesToCellName(j+1, i+1)
			f.SetCellValue(sheetName, cellName, cell)
		}
	}

	// 设置表头样式
	if len(data) > 0 {
		headerStyle, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{Bold: true},
			Fill: excelize.Fill{Type: "pattern", Color: []string{"CCCCCC"}, Pattern: 1},
		})
		headerRange := fmt.Sprintf("A1:%s1", string(rune('A'+len(data[0])-1)))
		f.SetCellStyle(sheetName, "A1", headerRange, headerStyle)
	}

	return f.SaveAs(filePath)
}

// exportToCSV 导出到 CSV 文件
func (a *App) exportToCSV(data [][]string, filePath string) error {
	file, err := os.Create(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	for _, row := range data {
		if err := writer.Write(row); err != nil {
			return err
		}
	}

	return nil
}

// importTableData 导入表数据
func (a *App) importTableData(tableName, filePath string) (int, error) {
	// 根据文件扩展名判断文件类型
	ext := strings.ToLower(filepath.Ext(filePath))

	var data [][]string
	var err error

	switch ext {
	case ".xlsx", ".xls":
		data, err = a.readExcelFile(filePath)
	case ".csv":
		data, err = a.readCSVFile(filePath)
	default:
		return 0, fmt.Errorf("不支持的文件格式: %s", ext)
	}

	if err != nil {
		return 0, fmt.Errorf("读取文件失败: %v", err)
	}

	if len(data) < 2 {
		return 0, fmt.Errorf("文件中没有足够的数据")
	}

	// 根据表名执行不同的导入逻辑
	switch tableName {
	case "admin_divisions":
		return a.importAdminDivisionsFromData(data)
	case "categories":
		return a.importCategoriesFromData(data)
	case "regions":
		return a.importRegionsFromData(data)
	default:
		return 0, fmt.Errorf("不支持导入到表: %s", tableName)
	}
}

// readExcelFile 读取 Excel 文件
func (a *App) readExcelFile(filePath string) ([][]string, error) {
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		return nil, fmt.Errorf("Excel文件中没有工作表")
	}

	return f.GetRows(sheetName)
}

// readCSVFile 读取 CSV 文件
func (a *App) readCSVFile(filePath string) ([][]string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	reader := csv.NewReader(file)
	return reader.ReadAll()
}

// executeSQL 执行 SQL 语句
func (a *App) executeSQL(sqlText string) (string, error) {
	sqlText = strings.TrimSpace(sqlText)
	if sqlText == "" {
		return "", fmt.Errorf("SQL 语句不能为空")
	}

	// 判断是查询还是执行语句
	upperSQL := strings.ToUpper(sqlText)
	if strings.HasPrefix(upperSQL, "SELECT") || strings.HasPrefix(upperSQL, "PRAGMA") {
		return a.executeQuery(sqlText)
	} else {
		return a.executeUpdate(sqlText)
	}
}

// executeQuery 执行查询语句
func (a *App) executeQuery(sqlText string) (string, error) {
	rows, err := database.DB.Raw(sqlText).Rows()
	if err != nil {
		return "", fmt.Errorf("执行查询失败: %v", err)
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return "", fmt.Errorf("获取列名失败: %v", err)
	}

	var result strings.Builder

	// 写入列名
	result.WriteString(strings.Join(columns, "\t"))
	result.WriteString("\n")
	result.WriteString(strings.Repeat("-", len(strings.Join(columns, "\t"))))
	result.WriteString("\n")

	// 读取数据
	rowCount := 0
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return "", fmt.Errorf("扫描数据失败: %v", err)
		}

		row := make([]string, len(columns))
		for i, val := range values {
			if val != nil {
				row[i] = fmt.Sprintf("%v", val)
			} else {
				row[i] = "NULL"
			}
		}

		result.WriteString(strings.Join(row, "\t"))
		result.WriteString("\n")
		rowCount++

		// 限制显示行数，避免界面卡顿
		if rowCount >= 1000 {
			result.WriteString(fmt.Sprintf("\n... (显示前1000行，总共可能更多)\n"))
			break
		}
	}

	if rowCount == 0 {
		result.WriteString("(无结果)\n")
	} else {
		result.WriteString(fmt.Sprintf("\n共 %d 行\n", rowCount))
	}

	return result.String(), nil
}

// executeUpdate 执行更新语句
func (a *App) executeUpdate(sqlText string) (string, error) {
	result := database.DB.Exec(sqlText)
	if result.Error != nil {
		return "", fmt.Errorf("执行更新失败: %v", result.Error)
	}

	return fmt.Sprintf("执行成功，影响行数: %d", result.RowsAffected), nil
}

// importAdminDivisionsFromData 导入行政区划数据
func (a *App) importAdminDivisionsFromData(data [][]string) (int, error) {
	if len(data) < 2 {
		return 0, fmt.Errorf("数据不足")
	}

	// 跳过标题行
	count := 0
	for i := 1; i < len(data); i++ {
		row := data[i]
		if len(row) < 2 {
			continue
		}

		code := strings.TrimSpace(row[0])
		unitName := strings.TrimSpace(row[1])

		if code == "" || unitName == "" {
			continue
		}

		// 使用 GORM 创建或更新记录
		err := database.DB.Exec("INSERT OR REPLACE INTO admin_divisions (code, unit_name, is_deleted) VALUES (?, ?, 0)",
			code, unitName).Error
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"code":     code,
				"unitName": unitName,
				"error":    err,
			}).Error("导入行政区划数据失败")
			continue
		}
		count++
	}

	return count, nil
}

// importCategoriesFromData 导入品类数据
func (a *App) importCategoriesFromData(data [][]string) (int, error) {
	if len(data) < 2 {
		return 0, fmt.Errorf("数据不足")
	}

	count := 0
	for i := 1; i < len(data); i++ {
		row := data[i]
		if len(row) < 2 {
			continue
		}

		code := strings.TrimSpace(row[0])
		name := strings.TrimSpace(row[1])

		if code == "" || name == "" {
			continue
		}

		err := database.DB.Exec("INSERT OR REPLACE INTO categories (code, name, is_deleted) VALUES (?, ?, 0)",
			code, name).Error
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"code":  code,
				"name":  name,
				"error": err,
			}).Error("导入品类数据失败")
			continue
		}
		count++
	}

	return count, nil
}

// importRegionsFromData 导入地区数据
func (a *App) importRegionsFromData(data [][]string) (int, error) {
	if len(data) < 2 {
		return 0, fmt.Errorf("数据不足")
	}

	count := 0
	for i := 1; i < len(data); i++ {
		row := data[i]
		if len(row) < 2 {
			continue
		}

		code := strings.TrimSpace(row[0])
		name := strings.TrimSpace(row[1])

		if code == "" || name == "" {
			continue
		}

		err := database.DB.Exec("INSERT OR REPLACE INTO regions (code, name, is_deleted) VALUES (?, ?, 0)",
			code, name).Error
		if err != nil {
			logger.Log.WithFields(logrus.Fields{
				"code":  code,
				"name":  name,
				"error": err,
			}).Error("导入地区数据失败")
			continue
		}
		count++
	}

	return count, nil
}
