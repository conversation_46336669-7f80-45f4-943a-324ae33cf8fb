package ocr

import (
	"fmt"
	"image"
	"os"
	"path/filepath"
	"strings"

	"github.com/disintegration/imaging"
	"simple_inventory_management_system/internal/logger"
)

// OCRService OCR识别服务
type OCRService struct {
	languages []string
}

// OCRResult OCR识别结果
type OCRResult struct {
	Text       string  `json:"text"`        // 识别的文本
	Confidence float64 `json:"confidence"`  // 识别置信度
	Language   string  `json:"language"`    // 识别的语言
	Error      error   `json:"error"`       // 错误信息
}

// TableCell 表格单元格
type TableCell struct {
	Text   string `json:"text"`
	Row    int    `json:"row"`
	Column int    `json:"column"`
}

// TableResult 表格识别结果
type TableResult struct {
	Cells []TableCell `json:"cells"`
	Rows  int         `json:"rows"`
	Cols  int         `json:"cols"`
	Error error       `json:"error"`
}

// NewOCRService 创建OCR服务实例
func NewOCRService() *OCRService {
	return &OCRService{
		languages: []string{"chi_sim", "chi_tra", "eng"}, // 简体中文、繁体中文、英文
	}
}

// RecognizeImage 识别图片中的文本（模拟实现）
func (s *OCRService) RecognizeImage(imagePath string) (*OCRResult, error) {
	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		return &OCRResult{Error: fmt.Errorf("图片文件不存在: %s", imagePath)}, err
	}

	// 预处理图片
	processedPath, err := s.preprocessImage(imagePath)
	if err != nil {
		logger.Log.WithField("error", err).Error("图片预处理失败")
		return &OCRResult{Error: err}, err
	}
	defer os.Remove(processedPath) // 清理临时文件

	// 模拟OCR识别结果
	// 在实际应用中，这里应该调用真正的OCR引擎
	fileName := filepath.Base(imagePath)
	mockText := s.generateMockOCRText(fileName)

	result := &OCRResult{
		Text:       mockText,
		Confidence: 85.5, // 模拟置信度
		Language:   strings.Join(s.languages, "+"),
	}

	logger.Log.WithField("confidence", result.Confidence).Info("OCR识别完成（模拟）")
	return result, nil
}

// RecognizeTable 识别图片中的表格（模拟实现）
func (s *OCRService) RecognizeTable(imagePath string) (*TableResult, error) {
	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		return &TableResult{Error: fmt.Errorf("图片文件不存在: %s", imagePath)}, err
	}

	// 预处理图片
	processedPath, err := s.preprocessImage(imagePath)
	if err != nil {
		logger.Log.WithField("error", err).Error("图片预处理失败")
		return &TableResult{Error: err}, err
	}
	defer os.Remove(processedPath) // 清理临时文件

	// 模拟表格识别结果
	cells := s.generateMockTableCells()

	result := &TableResult{
		Cells: cells,
		Rows:  s.getMaxRow(cells),
		Cols:  s.getMaxCol(cells),
	}

	logger.Log.WithField("rows", result.Rows).WithField("cols", result.Cols).Info("表格识别完成（模拟）")
	return result, nil
}

// preprocessImage 预处理图片以提高OCR识别率
func (s *OCRService) preprocessImage(imagePath string) (string, error) {
	// 打开图片
	src, err := imaging.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %v", err)
	}

	// 转换为灰度图
	gray := imaging.Grayscale(src)

	// 调整对比度和亮度
	enhanced := imaging.AdjustContrast(gray, 20)
	enhanced = imaging.AdjustBrightness(enhanced, 10)

	// 锐化
	sharpened := imaging.Sharpen(enhanced, 0.5)

	// 如果图片太小，进行放大
	bounds := sharpened.Bounds()
	if bounds.Dx() < 300 || bounds.Dy() < 300 {
		sharpened = imaging.Resize(sharpened, bounds.Dx()*2, bounds.Dy()*2, imaging.Lanczos)
	}

	// 保存预处理后的图片
	tempDir := os.TempDir()
	tempFile := filepath.Join(tempDir, fmt.Sprintf("ocr_temp_%d.png", os.Getpid()))

	err = imaging.Save(sharpened, tempFile)
	if err != nil {
		return "", fmt.Errorf("保存预处理图片失败: %v", err)
	}

	return tempFile, nil
}

// generateMockOCRText 生成模拟的OCR文本
func (s *OCRService) generateMockOCRText(fileName string) string {
	// 根据文件名生成不同的模拟文本
	mockTexts := []string{
		"这是一张包含中英文文本的图片。\nThis is an image containing Chinese and English text.\n\n示例内容：\n- 产品名称：智能手机\n- 价格：¥2999\n- 库存：100台",
		"发票信息\nInvoice Information\n\n公司名称：ABC科技有限公司\n地址：北京市朝阳区\n电话：010-12345678\n\n商品明细：\n1. 笔记本电脑 x1 ¥5999\n2. 鼠标 x1 ¥99\n总计：¥6098",
		"会议纪要\nMeeting Minutes\n\n时间：2025年1月15日\n地点：会议室A\n参会人员：张三、李四、王五\n\n议题：\n1. 项目进度汇报\n2. 下季度计划\n3. 预算审核",
	}
	
	// 根据文件名哈希选择模拟文本
	hash := 0
	for _, char := range fileName {
		hash += int(char)
	}
	
	return mockTexts[hash%len(mockTexts)]
}

// generateMockTableCells 生成模拟的表格单元格
func (s *OCRService) generateMockTableCells() []TableCell {
	return []TableCell{
		{Text: "姓名", Row: 0, Column: 0},
		{Text: "年龄", Row: 0, Column: 1},
		{Text: "职位", Row: 0, Column: 2},
		{Text: "张三", Row: 1, Column: 0},
		{Text: "28", Row: 1, Column: 1},
		{Text: "工程师", Row: 1, Column: 2},
		{Text: "李四", Row: 2, Column: 0},
		{Text: "32", Row: 2, Column: 1},
		{Text: "经理", Row: 2, Column: 2},
		{Text: "王五", Row: 3, Column: 0},
		{Text: "25", Row: 3, Column: 1},
		{Text: "设计师", Row: 3, Column: 2},
	}
}

// getMaxRow 获取最大行数
func (s *OCRService) getMaxRow(cells []TableCell) int {
	maxRow := 0
	for _, cell := range cells {
		if cell.Row > maxRow {
			maxRow = cell.Row
		}
	}
	return maxRow + 1
}

// getMaxCol 获取最大列数
func (s *OCRService) getMaxCol(cells []TableCell) int {
	maxCol := 0
	for _, cell := range cells {
		if cell.Column > maxCol {
			maxCol = cell.Column
		}
	}
	return maxCol + 1
}

// IsImageFile 检查是否为支持的图片文件
func IsImageFile(filePath string) bool {
	ext := strings.ToLower(filepath.Ext(filePath))
	supportedExts := []string{".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif"}
	
	for _, supportedExt := range supportedExts {
		if ext == supportedExt {
			return true
		}
	}
	return false
}

// GetImageFormat 获取图片格式
func GetImageFormat(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	_, format, err := image.DecodeConfig(file)
	if err != nil {
		return "", err
	}

	return format, nil
}
