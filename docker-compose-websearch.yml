version: '3.8'

services:
  open-websearch:
    image: ghcr.io/aas-ee/open-web-search:latest
    container_name: open-websearch-mcp
    ports:
      - "3000:3000"
    environment:
      - ENABLE_CORS=true
      - CORS_ORIGIN=*
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - websearch-network

networks:
  websearch-network:
    driver: bridge
