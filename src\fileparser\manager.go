package fileparser

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"
)

// FileInfo 文件信息
type FileInfo struct {
	ID          string    `json:"id"`          // 文件唯一标识
	OriginalName string   `json:"original_name"` // 原始文件名
	StoredPath   string   `json:"stored_path"`   // 存储路径
	FileType     FileType `json:"file_type"`     // 文件类型
	Size         int64    `json:"size"`          // 文件大小
	UploadTime   time.Time `json:"upload_time"`   // 上传时间
	ParsedContent string  `json:"parsed_content"` // 解析后的内容
	Status       string   `json:"status"`        // 状态：pending, parsed, error
	ErrorMsg     string   `json:"error_msg"`     // 错误信息
}

// FileManager 文件管理器
type FileManager struct {
	uploadDir   string                // 上传目录
	metadataFile string              // 元数据文件路径
	files       map[string]*FileInfo // 文件信息映射
	parser      *UniversalParser     // 文件解析器
	converter   *FileConverter       // 文件转换器
}

// NewFileManager 创建文件管理器
func NewFileManager(uploadDir string) *FileManager {
	metadataFile := filepath.Join(uploadDir, "files_metadata.json")
	
	fm := &FileManager{
		uploadDir:    uploadDir,
		metadataFile: metadataFile,
		files:        make(map[string]*FileInfo),
		parser:       NewUniversalParser(),
		converter:    NewFileConverter(),
	}
	
	// 确保上传目录存在
	os.MkdirAll(uploadDir, 0755)
	
	// 加载已有的文件元数据
	fm.loadMetadata()
	
	return fm
}

// generateFileID 生成文件ID
func (fm *FileManager) generateFileID() string {
	return fmt.Sprintf("file_%d", time.Now().UnixNano())
}

// UploadFile 上传文件
func (fm *FileManager) UploadFile(sourcePath, originalName string) (*FileInfo, error) {
	// 验证文件
	err := ValidateFile(sourcePath)
	if err != nil {
		return nil, err
	}

	// 获取文件信息
	fileInfo, err := os.Stat(sourcePath)
	if err != nil {
		return nil, fmt.Errorf("无法获取文件信息: %v", err)
	}

	// 生成文件ID和存储路径
	fileID := fm.generateFileID()
	ext := filepath.Ext(originalName)
	storedName := fileID + ext
	storedPath := filepath.Join(fm.uploadDir, storedName)

	// 复制文件到上传目录
	err = fm.copyFile(sourcePath, storedPath)
	if err != nil {
		return nil, fmt.Errorf("文件复制失败: %v", err)
	}

	// 检测文件类型
	fileType, err := fm.parser.detectFileType(storedPath)
	if err != nil {
		// 清理已复制的文件
		os.Remove(storedPath)
		return nil, fmt.Errorf("文件类型检测失败: %v", err)
	}

	// 创建文件信息
	info := &FileInfo{
		ID:           fileID,
		OriginalName: originalName,
		StoredPath:   storedPath,
		FileType:     fileType,
		Size:         fileInfo.Size(),
		UploadTime:   time.Now(),
		Status:       "pending",
	}

	// 保存文件信息
	fm.files[fileID] = info
	fm.saveMetadata()

	// 异步解析文件
	go fm.parseFileAsync(fileID)

	return info, nil
}

// parseFileAsync 异步解析文件
func (fm *FileManager) parseFileAsync(fileID string) {
	info, exists := fm.files[fileID]
	if !exists {
		return
	}

	// 解析文件
	result, err := fm.parser.Parse(info.StoredPath)
	if err != nil {
		info.Status = "error"
		info.ErrorMsg = err.Error()
	} else {
		info.Status = "parsed"
		info.ParsedContent = result.Content
		info.ErrorMsg = ""
	}

	// 保存更新后的元数据
	fm.saveMetadata()
}

// GetFile 获取文件信息
func (fm *FileManager) GetFile(fileID string) (*FileInfo, error) {
	info, exists := fm.files[fileID]
	if !exists {
		return nil, fmt.Errorf("文件不存在: %s", fileID)
	}
	return info, nil
}

// ListFiles 列出所有文件
func (fm *FileManager) ListFiles() []*FileInfo {
	var files []*FileInfo
	for _, info := range fm.files {
		files = append(files, info)
	}
	return files
}

// DeleteFile 删除文件
func (fm *FileManager) DeleteFile(fileID string) error {
	info, exists := fm.files[fileID]
	if !exists {
		return fmt.Errorf("文件不存在: %s", fileID)
	}

	// 删除物理文件
	err := os.Remove(info.StoredPath)
	if err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("删除文件失败: %v", err)
	}

	// 从内存中删除
	delete(fm.files, fileID)

	// 保存元数据
	fm.saveMetadata()

	return nil
}

// ConvertFile 转换文件
func (fm *FileManager) ConvertFile(fileID, targetFormat string) (*ConvertResult, error) {
	info, exists := fm.files[fileID]
	if !exists {
		return nil, fmt.Errorf("文件不存在: %s", fileID)
	}

	if info.Status != "parsed" {
		return nil, fmt.Errorf("文件尚未解析完成")
	}

	// 创建转换输出目录
	convertDir := filepath.Join(fm.uploadDir, "converted")
	
	return fm.converter.AutoConvert(info.StoredPath, convertDir, targetFormat)
}

// GetFileContent 获取文件解析后的内容
func (fm *FileManager) GetFileContent(fileID string) (string, error) {
	info, exists := fm.files[fileID]
	if !exists {
		return "", fmt.Errorf("文件不存在: %s", fileID)
	}

	if info.Status == "error" {
		return "", fmt.Errorf("文件解析失败: %s", info.ErrorMsg)
	}

	if info.Status == "pending" {
		return "", fmt.Errorf("文件正在解析中，请稍后再试")
	}

	return info.ParsedContent, nil
}

// copyFile 复制文件
func (fm *FileManager) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}

// loadMetadata 加载元数据
func (fm *FileManager) loadMetadata() {
	data, err := os.ReadFile(fm.metadataFile)
	if err != nil {
		// 文件不存在或读取失败，使用空的映射
		return
	}

	var files map[string]*FileInfo
	err = json.Unmarshal(data, &files)
	if err != nil {
		// JSON解析失败，使用空的映射
		return
	}

	fm.files = files
}

// saveMetadata 保存元数据
func (fm *FileManager) saveMetadata() {
	data, err := json.MarshalIndent(fm.files, "", "  ")
	if err != nil {
		return
	}

	os.WriteFile(fm.metadataFile, data, 0644)
}

// CleanupOldFiles 清理旧文件（超过指定天数的文件）
func (fm *FileManager) CleanupOldFiles(days int) error {
	cutoff := time.Now().AddDate(0, 0, -days)
	
	var toDelete []string
	for fileID, info := range fm.files {
		if info.UploadTime.Before(cutoff) {
			toDelete = append(toDelete, fileID)
		}
	}

	for _, fileID := range toDelete {
		fm.DeleteFile(fileID)
	}

	return nil
}

// GetStats 获取统计信息
func (fm *FileManager) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"total_files": len(fm.files),
		"by_type":     make(map[string]int),
		"by_status":   make(map[string]int),
		"total_size":  int64(0),
	}

	for _, info := range fm.files {
		// 按类型统计
		typeStr := GetFileTypeString(info.FileType)
		if count, exists := stats["by_type"].(map[string]int)[typeStr]; exists {
			stats["by_type"].(map[string]int)[typeStr] = count + 1
		} else {
			stats["by_type"].(map[string]int)[typeStr] = 1
		}

		// 按状态统计
		if count, exists := stats["by_status"].(map[string]int)[info.Status]; exists {
			stats["by_status"].(map[string]int)[info.Status] = count + 1
		} else {
			stats["by_status"].(map[string]int)[info.Status] = 1
		}

		// 总大小
		stats["total_size"] = stats["total_size"].(int64) + info.Size
	}

	return stats
}
